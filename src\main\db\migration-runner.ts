import { Database } from 'better-sqlite3';
import fs from 'fs';
import path from 'path';

interface Migration {
  version: number;
  name: string;
  sql: string;
}

export class MigrationRunner {
  private db: Database;

  constructor(db: Database) {
    this.db = db;
    this.ensureMigrationsTable();
  }

  private ensureMigrationsTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
  }

  private getAppliedMigrations(): Set<number> {
    const rows = this.db.prepare('SELECT version FROM schema_migrations').all() as { version: number }[];
    return new Set(rows.map(row => row.version));
  }

  private loadMigrations(): Migration[] {
    // Try multiple possible locations for migrations
    const possiblePaths = [
      path.join(__dirname, 'migrations'),
      path.join(process.cwd(), 'src/main/db/migrations'),
      path.join(process.cwd(), 'out/main/db/migrations')
    ];
    
    let migrationsDir: string | null = null;
    for (const p of possiblePaths) {
      if (fs.existsSync(p)) {
        migrationsDir = p;
        break;
      }
    }
    
    if (!migrationsDir) {
      console.log('[Migrations] No migrations directory found in:', possiblePaths);
      return [];
    }
    
    console.log('[Migrations] Found migrations directory at:', migrationsDir);

    const files = fs.readdirSync(migrationsDir)
      .filter(f => f.endsWith('.sql'))
      .sort();

    const migrations: Migration[] = [];

    for (const file of files) {
      // Extract version from filename (e.g., 001_initial.sql -> 1)
      const match = file.match(/^(\d+)_(.+)\.sql$/);
      if (!match) {
        console.warn(`[Migrations] Skipping invalid migration filename: ${file}`);
        continue;
      }

      const version = parseInt(match[1], 10);
      const name = match[2];
      const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');

      migrations.push({ version, name, sql });
    }

    return migrations.sort((a, b) => a.version - b.version);
  }

  public runMigrations(): void {
    console.log('[Migrations] Starting migration process...');
    
    const applied = this.getAppliedMigrations();
    const migrations = this.loadMigrations();
    
    let ranCount = 0;

    for (const migration of migrations) {
      if (applied.has(migration.version)) {
        console.log(`[Migrations] Skipping already applied: ${migration.version}_${migration.name}`);
        continue;
      }

      console.log(`[Migrations] Applying: ${migration.version}_${migration.name}`);
      
      try {
        this.db.transaction(() => {
          // For migrations that add columns, we need to handle errors gracefully
          // since SQLite throws if column already exists
          if (migration.name.includes('add_sync_columns')) {
            const statements = migration.sql.split(';').filter(s => s.trim());
            for (const statement of statements) {
              if (statement.trim()) {
                try {
                  this.db.exec(statement);
                } catch (err: any) {
                  // Ignore "duplicate column name" errors
                  if (!err.message?.includes('duplicate column name')) {
                    throw err;
                  }
                  console.log(`[Migrations] Column already exists, skipping: ${statement.trim().substring(0, 50)}...`);
                }
              }
            }
          } else {
            // Run the migration normally
            this.db.exec(migration.sql);
          }
          
          // Record that it was applied
          this.db.prepare(
            'INSERT INTO schema_migrations (version, name) VALUES (?, ?)'
          ).run(migration.version, migration.name);
        })();
        
        ranCount++;
        console.log(`[Migrations] Successfully applied: ${migration.version}_${migration.name}`);
      } catch (error) {
        console.error(`[Migrations] Failed to apply ${migration.version}_${migration.name}:`, error);
        throw error;
      }
    }

    if (ranCount === 0) {
      console.log('[Migrations] No new migrations to apply');
    } else {
      console.log(`[Migrations] Applied ${ranCount} migration(s)`);
    }
  }
}
