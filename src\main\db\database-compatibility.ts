import path from 'path';
import fs from 'fs';
import { getDatabasePath, isUsingOptimizedDatabase } from './config';
import { v4 as uuidv4 } from 'uuid';
// Import our custom better-sqlite3 helper
import { createDatabase } from './better-sqlite3-helper';
import type { Database } from 'better-sqlite3';
// Import color utilities from consolidated module
import {
  hexToRgb
} from '../../shared/utils/color';

/** Database instance (better-sqlite3) */
let db: Database | null = null;

// --- Row interfaces (compatibility layer) ---

interface ColorRow {
  id: string;
  product: string;
  name: string;
  code: string;
  hex: string;
  cmyk: string;
  notes?: string;
  gradient?: string;
  isLibrary?: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

// --- New optimized row interfaces ---

/**
 * Initialize the database connection and ensure schema exists
 * @returns Database instance
 */
export function initDatabase(): Database {
  if (db) {return db;}

  try {
    const dbPath = getDatabasePath();
    console.log(`[DB] Initializing database at: ${dbPath}`);

    // Ensure directory exists
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    db = createDatabase(dbPath);

    if (!db) {
      throw new Error('Failed to create database instance');
    }

    // Check if using optimized database
    if (isUsingOptimizedDatabase()) {
      console.log('[DB] Using optimized database schema');
      initOptimizedSchema();
    } else {
      console.log('[DB] Using legacy database schema');
      initLegacySchema();
    }

    return db;
  } catch (error) {
    console.error('[DB] Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Initialize legacy schema (for backward compatibility)
 */
function initLegacySchema(): void {
  if (!db) {throw new Error('Database not initialized');}

  // Enable foreign key constraints
  db.exec('PRAGMA foreign_keys = ON');

  // Create legacy tables
  db.exec(`
    CREATE TABLE IF NOT EXISTS products (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );

    CREATE TABLE IF NOT EXISTS colors (
      id TEXT PRIMARY KEY,
      product TEXT NOT NULL,
      name TEXT NOT NULL,
      code TEXT NOT NULL,
      hex TEXT NOT NULL,
      cmyk TEXT NOT NULL,
      notes TEXT,
      gradient TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );

    -- REMOVED: product_selections table (
      id TEXT PRIMARY KEY,
      product_id TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (product_id) REFERENCES products(id)
    );

    -- REMOVED: selection_colors table (
      id TEXT PRIMARY KEY,
      selection_id TEXT NOT NULL,
      color_id TEXT NOT NULL,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (selection_id) REFERENCES product_selections(id),
      FOREIGN KEY (color_id) REFERENCES colors(id)
    );
  `);

  // Create indexes
  db.exec(`
  `);
}

/**
 * Initialize optimized schema (already created, just set pragmas)
 */
function initOptimizedSchema(): void {
  if (!db) {throw new Error('Database not initialized');}

  // Apply optimal PRAGMA settings
  db.exec(`
    PRAGMA foreign_keys = ON;
    PRAGMA journal_mode = WAL;
    PRAGMA synchronous = NORMAL;
    PRAGMA temp_store = MEMORY;
    PRAGMA mmap_size = 30000000000;
    PRAGMA cache_size = -64000;
    PRAGMA wal_autocheckpoint = 1000;
  `);
}

// --- Color Management with Compatibility Layer ---

/**
 * Get all colors (compatibility layer)
 */
export function getAllColors(): ColorRow[] {
  const db = getDatabase();
  
  if (isUsingOptimizedDatabase()) {
    // Query from optimized schema using view
    const rows = db.prepare(`
      SELECT 
        external_id,
        source,
        code,
        display_name,
        hex,
        cyan,
        magenta,
        yellow,
        black,
        properties,
        created_at,
        updated_at
      FROM v_colors
      WHERE is_active = 1
    `).all() as any[];

    // Convert to legacy format
    return rows.map((row: any) => ({
      id: row.external_id,
      product: row.properties?.product || '',
      name: row.display_name || row.code,
      code: row.code,
      hex: row.hex,
      cmyk: `${row.cyan || 0},${row.magenta || 0},${row.yellow || 0},${row.black || 0}`,
      notes: row.properties?.notes || undefined,
      gradient: row.properties?.gradient || undefined,
      isLibrary: row.source !== 'user' ? 1 : 0,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));
  } else {
    // Query from legacy schema
    return db.prepare('SELECT * FROM colors').all() as ColorRow[];
  }
}

/**
 * Get color by ID (compatibility layer)
 */
export function getColorById(id: string): ColorRow | undefined {
  const db = getDatabase();
  
  if (isUsingOptimizedDatabase()) {
    // Query using external_id
    const row = db.prepare(`
      SELECT 
        external_id,
        source,
        code,
        display_name,
        hex,
        cyan,
        magenta,
        yellow,
        black,
        properties,
        created_at,
        updated_at
      FROM v_colors
      WHERE external_id = ? AND is_active = 1
    `).get(id) as {
      external_id: string;
      source: string;
      code: string;
      display_name?: string;
      hex: string;
      cyan?: number;
      magenta?: number;
      yellow?: number;
      black?: number;
      properties?: any;
      created_at: string;
      updated_at: string;
    } | undefined;

    if (!row) {return undefined;}

    // Convert to legacy format
    return {
      id: row.external_id,
      product: row.properties?.product || '',
      name: row.display_name || row.code,
      code: row.code,
      hex: row.hex,
      cmyk: `${row.cyan || 0},${row.magenta || 0},${row.yellow || 0},${row.black || 0}`,
      notes: row.properties?.notes || undefined,
      gradient: row.properties?.gradient || undefined,
      isLibrary: row.source !== 'user' ? 1 : 0,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  } else {
    // Query from legacy schema
    return db.prepare('SELECT * FROM colors WHERE id = ?').get(id) as ColorRow | undefined;
  }
}

/**
 * Add new color (compatibility layer)
 */
export function addColor(color: Omit<ColorRow, 'id' | 'createdAt' | 'updatedAt'>): ColorRow {
  const db = getDatabase();
  const id = uuidv4();
  const now = new Date().toISOString();

  if (isUsingOptimizedDatabase()) {
    // Begin transaction for atomic operation
    const insertColor = db.transaction(() => {
      // Insert into colors table
      const colorInfo = db.prepare(`
        INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, is_metallic, is_effect, properties, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        color.isLibrary ? 2 : 1, // 2 for PANTONE, 1 for user
        color.code,
        color.name,
        color.hex,
        color.gradient ? 1 : 0,
        0, // is_metallic default
        0, // is_effect default
        JSON.stringify({ 
          product: color.product, 
          notes: color.notes,
          gradient: color.gradient
        }),
        now,
        now
      );

      const colorId = colorInfo.lastInsertRowid as number;

      // Parse and insert CMYK values
      if (color.cmyk) {
        const [c, m, y, k] = color.cmyk.split(',').map(v => parseInt(v.trim()) || 0);
        db.prepare(`
          INSERT INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(colorId, c, m, y, k);
      }

      // Insert RGB values (converted from hex)
      const rgb = hexToRgb(color.hex);
      if (rgb) {
        db.prepare(`
          INSERT INTO color_rgb (color_id, r, g, b)
          VALUES (?, ?, ?, ?)
        `).run(colorId, rgb.r, rgb.g, rgb.b);
      }

      return colorId;
    });

    insertColor();

    // Return the new color in legacy format
    return {
      id,
      ...color,
      createdAt: now,
      updatedAt: now
    };
  } else {
    // Insert into legacy schema
    db.prepare(`
      INSERT INTO colors (id, product, name, code, hex, cmyk, notes, gradient, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      color.product,
      color.name,
      color.code,
      color.hex,
      color.cmyk,
      color.notes || null,
      color.gradient || null,
      now,
      now
    );

    return {
      id,
      ...color,
      createdAt: now,
      updatedAt: now
    };
  }
}

/**
 * Update color (compatibility layer)
 */
export function updateColor(id: string, updates: Partial<Omit<ColorRow, 'id' | 'createdAt' | 'updatedAt'>>): ColorRow | undefined {
  const db = getDatabase();
  const now = new Date().toISOString();

  if (isUsingOptimizedDatabase()) {
    // Get current color to find internal ID
    const current = db.prepare('SELECT id FROM colors WHERE external_id = ?').get(id) as any;
    if (!current) {return undefined;}

    const updateColor = db.transaction(() => {
      // Update colors table
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updates.code !== undefined) {
        updateFields.push('code = ?');
        updateValues.push(updates.code);
      }
      if (updates.name !== undefined) {
        updateFields.push('display_name = ?');
        updateValues.push(updates.name);
      }
      if (updates.hex !== undefined) {
        updateFields.push('hex = ?');
        updateValues.push(updates.hex);
      }
      if (updates.gradient !== undefined) {
        updateFields.push('is_gradient = ?');
        updateValues.push(updates.gradient ? 1 : 0);
      }

      // Update properties
      if (updates.product !== undefined || updates.notes !== undefined || updates.gradient !== undefined) {
        const currentProps = db.prepare('SELECT properties FROM colors WHERE id = ?').get(current.id) as {properties?: string} | undefined;
        const props = currentProps?.properties ? JSON.parse(currentProps.properties) : {};
        
        if (updates.product !== undefined) {props.product = updates.product;}
        if (updates.notes !== undefined) {props.notes = updates.notes;}
        if (updates.gradient !== undefined) {props.gradient = updates.gradient;}
        
        updateFields.push('properties = ?');
        updateValues.push(JSON.stringify(props));
      }

      updateFields.push('updated_at = ?');
      updateValues.push(now);

      if (updateFields.length > 0) {
        updateValues.push(current.id);
        db.prepare(`UPDATE colors SET ${updateFields.join(', ')} WHERE id = ?`).run(...updateValues);
      }

      // Update CMYK if provided
      if (updates.cmyk !== undefined) {
        const [c, m, y, k] = updates.cmyk.split(',').map(v => parseInt(v.trim()) || 0);
        db.prepare(`
          INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(current.id, c, m, y, k);
      }

      // Update RGB if hex changed
      if (updates.hex !== undefined) {
        const rgb = hexToRgb(updates.hex);
        if (rgb) {
          db.prepare(`
            INSERT OR REPLACE INTO color_rgb (color_id, r, g, b)
            VALUES (?, ?, ?, ?)
          `).run(current.id, rgb.r, rgb.g, rgb.b);
        }
      }
    });

    updateColor();
    return getColorById(id);
  } else {
    // Update in legacy schema
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    if (updateFields.length === 0) {return getColorById(id);}

    updateFields.push('updated_at = ?');
    updateValues.push(now);
    updateValues.push(id);

    db.prepare(`UPDATE colors SET ${updateFields.join(', ')} WHERE id = ?`).run(...updateValues);
    return getColorById(id);
  }
}

/**
 * Delete color (compatibility layer)
 */
export function deleteColor(id: string): boolean {
  const db = getDatabase();

  if (isUsingOptimizedDatabase()) {
    // Soft delete in optimized schema
    const result = db.prepare(`
      UPDATE colors 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE external_id = ? AND deleted_at IS NULL
    `).run(id);
    return result.changes > 0;
  } else {
    // Hard delete in legacy schema
    const result = db.prepare('DELETE FROM colors WHERE id = ?').run(id);
    return result.changes > 0;
  }
}

// --- Helper Functions ---

export function getDatabase(): Database {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }
  return db;
}

export function closeDatabase(): void {
  if (db) {
    db.close();
    db = null;
  }
}

// Export for use in services
export const database = {
  init: initDatabase,
  get: getDatabase,
  close: closeDatabase,
  colors: {
    getAll: getAllColors,
    getById: getColorById,
    add: addColor,
    update: updateColor,
    delete: deleteColor
  }
};
