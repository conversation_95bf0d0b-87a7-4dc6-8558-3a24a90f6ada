/**
 * @file ColorForm.tsx
 * @description Form component for adding/editing flat color entries with Apple-inspired UI
 * Enhanced with test IDs for Puppeteer, improved validation, and better accessibility
 */

import React, { useCallback, useMemo } from 'react';
import { ColorEntry } from '../../shared/types/color.types';
import { useColorStore } from '../store/color.store';
import { useColorForm } from '../hooks/useColorForm';
import { parseCMYK, formatCMYKForBackend, standardizeHex, hexToCmyk } from '../../shared/utils/color';
import ColorPreview from './ColorPreview';
import FormInput from './FormInput';
import tokens from '../styles/tokens';

// Form state type - Unused but kept for reference
/*
interface FormState {
  product: string;
  name: string;  // Previously "flavour"
  code: string;  // Previously "pantone"
  hex: string;
  cmyk: string;
  notes: string;
}
*/

interface ColorFormProps {
  editMode?: boolean;
  color?: ColorEntry;
  onSuccess?: (newColorId?: string) => void;
  onCancel?: () => void;
  isModal?: boolean;
  hideProductField?: boolean;
  defaultProduct?: string;
  prefillMode?: boolean; // When true, use color data to prefill but create new color
}

export default function ColorForm({ editMode = false, color, onSuccess, onCancel, isModal = false, hideProductField = false, defaultProduct = '', prefillMode = false }: ColorFormProps) {
  const { addColor, updateColor } = useColorStore();
  const {
    formData,
    validation,
    isSubmitting,
    error,
    isDarkColor,
    setField,
    setHexFromColorPicker,
    startSubmission,
    submissionSuccessful,
    submissionFailed,
    resetForm
  } = useColorForm(color, defaultProduct);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setField(name as "product" | "name" | "code" | "hex" | "cmyk" | "notes" | "gradient" | "isLibrary", value);
  }, [setField]);

  const handleColorPickerChange = useCallback((hexColor: string) => {
    setHexFromColorPicker(hexColor);
  }, [setHexFromColorPicker]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    const canProceed = startSubmission();
    if (!canProceed) {return;}

    try {
      // Parse and format CMYK to ensure it's in the correct format for the backend
      let cmykObj;
      let formattedCmyk;

      // If CMYK is empty, derive it from hex
      if (!formData.cmyk || formData.cmyk.trim() === '') {
        const derivedCmyk = hexToCmyk(standardizeHex(formData.hex));
        if (derivedCmyk) {
          cmykObj = derivedCmyk;
          formattedCmyk = formatCMYKForBackend(cmykObj);
        } else {
          // Fallback to default CMYK values
          cmykObj = { c: 0, m: 0, y: 0, k: 0 };
          formattedCmyk = 'C:0 M:0 Y:0 K:0';
        }
      } else {
        // Use provided CMYK
        cmykObj = parseCMYK(formData.cmyk);
        formattedCmyk = formatCMYKForBackend(cmykObj);
      }

      // Prepare data for flat color with formatted CMYK
      const submitData = {
        ...formData,
        product: hideProductField ? defaultProduct : formData.product,
        hex: standardizeHex(formData.hex), // Ensure hex is 6-digit format
        cmyk: formattedCmyk,
        gradient: undefined
      };

      if (editMode && color && !prefillMode) {
        // Update existing color - only include fields that changed
        const updates = Object.entries(submitData)
          .filter(([key, value]) => {
            // Special handling for CMYK to compare parsed values rather than string format
            if (key === 'cmyk') {
              const originalCmykObj = parseCMYK(color.cmyk);
              const newCmykObj = parseCMYK(value as string);
              return (
                originalCmykObj.c !== newCmykObj.c ||
                originalCmykObj.m !== newCmykObj.m ||
                originalCmykObj.y !== newCmykObj.y ||
                originalCmykObj.k !== newCmykObj.k
              );
            }
            return value !== (color as ColorEntry)[key as keyof ColorEntry];
          })
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

        const result = await updateColor(color.id, updates);
        if (result) {
          submissionSuccessful();
          onSuccess?.();
        }
      } else {
        // Add new color (either completely new or prefilled from library)
        const result = await addColor(submitData);
        if (result) {
          resetForm();
          submissionSuccessful();
          onSuccess?.(result.id);
        }
      }
    } catch (err: unknown) {
      submissionFailed(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  // Memoize the form title and description text
  const formHeaderContent = useMemo(() => {
    if (editMode) {
      return {
        title: 'Edit Color',
        description: 'Update the properties of this color entry.'
      };
    } else if (prefillMode && color) {
      return {
        title: `Customize ${color.code || color.name || 'Color'}`,
        description: 'Modify the details as needed for this product. This will create a new color entry.'
      };
    } else {
      return {
        title: 'Add Flat Color',
        description: 'Fill in the details to add a new color to your product.'
      };
    }
  }, [editMode, prefillMode, color]);


  const formClasses = isModal
    ? "bg-ui-background-primary dark:bg-zinc-900 rounded-lg shadow-sm w-full"
    : "bg-ui-background-primary dark:bg-zinc-900 rounded-lg shadow-sm p-5 border border-ui-border-light dark:border-zinc-700 w-full";

  return (
    <div
      className={formClasses}
      data-testid="flat-color-form"
    >
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-3">
        <div className="w-full">
          <h2
            className="text-xl font-semibold text-ui-foreground-primary dark:text-white mb-1"
            data-testid="form-title"
          >
            {formHeaderContent.title}
          </h2>
          <p
            className="text-xs text-ui-foreground-secondary dark:text-gray-400"
          >
            {formHeaderContent.description}
          </p>
        </div>

        <div className="flex-shrink-0">
          <ColorPreview
            hex={formData.hex}
            colorName={formData.name || 'New Color'}
            onChange={handleColorPickerChange}
            isDarkColor={isDarkColor}
            data-testid="color-preview"
          />
        </div>
      </div>

      {error && (
        <div
          className="mb-4 bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800 text-sm flex items-start text-red-600 dark:text-red-400"
          data-testid="error-message"
          role="alert"
          aria-live="assertive"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} data-testid="color-form-element">
        <div className="grid gap-y-3 w-full">
          {/* Product Name - Full Width */}
          {!hideProductField && (
            <div className="w-full">
              <label className="block text-ui-foreground-secondary text-sm mb-1" htmlFor="product">
                Product
              </label>
              <FormInput
                id="product"
                name="product"
                value={formData.product}
                onChange={handleChange}
                required
                placeholder="Product name"
                isValid={validation.product || formData.product === ''}
                errorMessage="Product name is required"
                hideLabel={true}
                aria-label="Product Name"
                aria-required="true"
                aria-invalid={!validation.product && formData.product !== ''}
                data-testid="product-input"
                inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
              />
            </div>
          )}

          {/* First row - Flavor Name and Color */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
            <div className="w-full">
              <FormInput
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder="Color name"
                isValid={validation.name || formData.name === ''}
                errorMessage="Color name is required"
                hideLabel={true}
                aria-label="Color Name"
                aria-required="true"
                aria-invalid={!validation.name && formData.name !== ''}
                data-testid="name-input"
                inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
              />
            </div>

            <div className="w-full">
              <FormInput
                id="hex"
                name="hex"
                value={formData.hex.replace('#', '')}
                onChange={(e) => handleChange({
                  ...e,
                  target: { ...e.target, value: '#' + e.target.value, name: 'hex' }
                })}
                required
                placeholder="HEX"
                isValid={validation.hex || formData.hex === ''}
                errorMessage="Valid hex format required (e.g. FF5733)"
                prefix="#"
                hideLabel={true}
                aria-label="HEX Color Code"
                aria-required="true"
                aria-invalid={!validation.hex && formData.hex !== ''}
                data-testid="hex-input"
                inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
              />
            </div>
          </div>

          {/* Second row - Pantone and CMYK */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
            <div className="w-full">
              <FormInput
                id="code"
                name="code"
                value={formData.code}
                onChange={handleChange}
                required
                placeholder="Color code"
                isValid={validation.code || formData.code === ''}
                errorMessage="Color code is required"
                hideLabel={true}
                aria-label="Color Code"
                aria-required="true"
                aria-invalid={!validation.code && formData.code !== ''}
                data-testid="code-input"
                inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
              />
            </div>

            <div className="w-full">
              <FormInput
                id="cmyk"
                name="cmyk"
                value={formData.cmyk}
                onChange={handleChange}
                required
                placeholder="C M Y K"
                isValid={validation.cmyk || formData.cmyk === ''}
                errorMessage="Valid CMYK format required (e.g. 0,100,81,4 or C:0 M:100 Y:81 K:4)"
                hideLabel={true}
                aria-label="CMYK Values"
                aria-required="true"
                aria-invalid={!validation.cmyk && formData.cmyk !== ''}
                data-testid="cmyk-input"
                inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
              />
            </div>
          </div>

          {/* Notes Field - Full Width */}
          <div className="mt-3 w-full">
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Add any details about this color here..."
              className="w-full p-3 border border-gray-200 dark:border-zinc-700 rounded-md bg-gray-50 dark:bg-zinc-800 focus:outline-none focus:ring-1 focus:ring-pantone-red focus:border-transparent transition-all resize-none text-gray-600 dark:text-gray-300 text-xs"
              rows={3}
              aria-label="Additional Notes"
              data-testid="notes-input"
            />
          </div>

          {/* Buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 w-full">
            <button
              type="button"
              className="px-4 py-2 text-xs font-medium rounded-md border border-gray-200 dark:border-zinc-700 text-gray-600 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors shadow-sm focus:outline-none focus:ring-1 focus:ring-gray-300 dark:focus:ring-gray-600 focus:ring-opacity-50 flex items-center justify-center w-full"
              onClick={onCancel}
              disabled={isSubmitting}
              aria-label="Cancel"
              data-testid="cancel-button"
            >
              Cancel
            </button>

            <button
              type="submit"
              className="px-4 py-2 text-xs font-medium rounded-md text-white flex items-center justify-center transition-colors shadow-sm w-full"
              style={{
                backgroundColor: tokens.colors.brand.primary,
                borderRadius: tokens.borderRadius.md,
                boxShadow: tokens.shadows.sm,
              }}
              disabled={isSubmitting}
              aria-label={editMode ? "Update Color" : "Save Color"}
              data-testid="submit-button"
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                  </svg>
                  Saving...
                </span>
              ) : (
                <span>
                  {editMode ? 'Update Color' : 'Save Color'}
                </span>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
