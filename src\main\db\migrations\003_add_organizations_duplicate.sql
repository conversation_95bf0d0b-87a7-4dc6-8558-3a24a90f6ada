-- Migration: Add multi-tenant organization support
-- Version: 003
-- Date: 2025-05-30

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id INTEGER PRIMARY KEY,
    external_id TEXT UNIQUE NOT NULL DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
    settings TEXT DEFAULT '{}', -- JSO<PERSON> stored as TEXT in SQLite
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organization_members table
CREATE TABLE IF NOT EXISTS organization_members (
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL, -- UUID as TEXT in SQLite
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    invited_by TEXT, -- UUID as TEXT
    PRIMARY KEY (organization_id, user_id)
);

-- Add organization_id to existing tables
ALTER TABLE colors ADD COLUMN organization_id INTEGER REFERENCES organizations(id);
ALTER TABLE products ADD COLUMN organization_id INTEGER REFERENCES organizations(id);
ALTER TABLE product_colors ADD COLUMN organization_id INTEGER REFERENCES organizations(id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id);
CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

-- Update triggers for updated_at on organizations
CREATE TRIGGER IF NOT EXISTS update_organizations_updated_at
AFTER UPDATE ON organizations
FOR EACH ROW
BEGIN
    UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Note: In SQLite, we don't have RLS policies. Access control is handled in the application layer.
-- The user_id columns remain for audit purposes but are no longer used for access control.
