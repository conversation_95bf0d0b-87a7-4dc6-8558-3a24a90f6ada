/**
 * Advanced connection pooling using generic-pool
 * Provides enterprise-level database connection management
 */

import * as genericPool from 'generic-pool';
import path from 'path';
import { app } from 'electron';

// Dynamic import to avoid bundler issues
let Database: any;

interface PoolConnection {
  db: any;
  id: string;
  createdAt: number;
  lastUsed: number;
  queryCount: number;
}

interface PoolStats {
  size: number;
  available: number;
  borrowed: number;
  invalid: number;
  pending: number;
  max: number;
  min: number;
}

export class AdvancedDatabasePool {
  private static instance: AdvancedDatabasePool;
  private pool: genericPool.Pool<PoolConnection> | null = null;
  private dbPath: string;
  private isInitialized = false;
  private stats = {
    totalConnections: 0,
    totalQueries: 0,
    totalErrors: 0,
    averageResponseTime: 0,
    responseTimes: [] as number[]
  };

  private constructor() {
    this.dbPath = path.join(app.getPath('userData'), 'chromasync.db');
  }

  static getInstance(): AdvancedDatabasePool {
    if (!AdvancedDatabasePool.instance) {
      AdvancedDatabasePool.instance = new AdvancedDatabasePool();
    }
    return AdvancedDatabasePool.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Dynamic import of better-sqlite3
      Database = (await import('better-sqlite3')).default;
      
      // Create the pool with advanced configuration
      this.pool = genericPool.createPool({
        create: async (): Promise<PoolConnection> => {
          console.log('[AdvancedPool] Creating new database connection');
          const db = new Database(this.dbPath, {
            verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
            fileMustExist: false,
            timeout: 5000
          });

          // Optimize database settings
          db.pragma('journal_mode = WAL');
          db.pragma('synchronous = NORMAL');
          db.pragma('cache_size = -64000'); // 64MB cache
          db.pragma('foreign_keys = ON');
          db.pragma('temp_store = MEMORY');
          db.pragma('mmap_size = 268435456'); // 256MB memory mapping

          const connection: PoolConnection = {
            db,
            id: `conn_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            queryCount: 0
          };

          this.stats.totalConnections++;
          return connection;
        },
        destroy: async (connection: PoolConnection): Promise<void> => {
          console.log(`[AdvancedPool] Destroying connection ${connection.id}`);
          try {
            connection.db.close();
          } catch (error) {
            console.error('[AdvancedPool] Error closing connection:', error);
          }
        },
        validate: async (connection: PoolConnection): Promise<boolean> => {
          try {
            // Check if connection is still valid
            connection.db.prepare('SELECT 1').get();
            
            // Check connection age (max 1 hour)
            const maxAge = 60 * 60 * 1000; // 1 hour
            const age = Date.now() - connection.createdAt;
            
            return age < maxAge;
          } catch (error) {
            console.error('[AdvancedPool] Connection validation failed:', error);
            return false;
          }
        }
      }, {
        max: 10,           // Maximum pool size
        min: 2,            // Minimum pool size
        acquireTimeoutMillis: 30000,  // 30 seconds timeout
        createTimeoutMillis: 5000,    // 5 seconds to create connection
        destroyTimeoutMillis: 5000,   // 5 seconds to destroy connection
        idleTimeoutMillis: 300000,    // 5 minutes idle timeout
        reapIntervalMillis: 60000,    // 1 minute reap interval
        maxWaitingClients: 50,        // Max waiting clients
        testOnBorrow: true,           // Test connection before use
        testOnReturn: false,          // Don't test on return
        evictionRunIntervalMillis: 180000,  // 3 minutes eviction interval
        numTestsPerEvictionRun: 3,    // Test 3 connections per eviction run
        softIdleTimeoutMillis: 180000,  // 3 minutes soft idle timeout
        priorityRange: 2,             // Priority range for borrowing
        fifo: true,                   // First in, first out
        autostart: true               // Start the pool immediately
      });

      this.isInitialized = true;
      console.log('[AdvancedPool] Advanced database pool initialized successfully');

      // Set up pool event handlers
      this.pool.on('factoryCreateError', (err) => {
        console.error('[AdvancedPool] Factory create error:', err);
        this.stats.totalErrors++;
      });

      this.pool.on('factoryDestroyError', (err) => {
        console.error('[AdvancedPool] Factory destroy error:', err);
        this.stats.totalErrors++;
      });

    } catch (error) {
      console.error('[AdvancedPool] Failed to initialize pool:', error);
      throw error;
    }
  }

  async executeQuery<T>(query: string, params: any[] = []): Promise<T> {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    const startTime = Date.now();
    let connection: PoolConnection | null = null;

    try {
      // Acquire connection from pool
      connection = await this.pool.acquire();
      connection.lastUsed = Date.now();
      connection.queryCount++;

      // Execute query
      const stmt = connection.db.prepare(query);
      const result = params.length > 0 ? stmt.all(...params) : stmt.all();

      // Update statistics
      const responseTime = Date.now() - startTime;
      this.updateStats(responseTime);

      return result as T;
    } catch (error) {
      this.stats.totalErrors++;
      console.error('[AdvancedPool] Query execution error:', error);
      throw error;
    } finally {
      // Release connection back to pool
      if (connection && this.pool) {
        await this.pool.release(connection);
      }
    }
  }

  async executeQuerySingle<T>(query: string, params: any[] = []): Promise<T | null> {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    const startTime = Date.now();
    let connection: PoolConnection | null = null;

    try {
      connection = await this.pool.acquire();
      connection.lastUsed = Date.now();
      connection.queryCount++;

      const stmt = connection.db.prepare(query);
      const result = params.length > 0 ? stmt.get(...params) : stmt.get();

      const responseTime = Date.now() - startTime;
      this.updateStats(responseTime);

      return result as T || null;
    } catch (error) {
      this.stats.totalErrors++;
      console.error('[AdvancedPool] Single query execution error:', error);
      throw error;
    } finally {
      if (connection && this.pool) {
        await this.pool.release(connection);
      }
    }
  }

  async executeUpdate(query: string, params: any[] = []): Promise<{ changes: number; lastInsertRowid: number }> {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    const startTime = Date.now();
    let connection: PoolConnection | null = null;

    try {
      connection = await this.pool.acquire();
      connection.lastUsed = Date.now();
      connection.queryCount++;

      const stmt = connection.db.prepare(query);
      const result = params.length > 0 ? stmt.run(...params) : stmt.run();

      const responseTime = Date.now() - startTime;
      this.updateStats(responseTime);

      return {
        changes: result.changes,
        lastInsertRowid: result.lastInsertRowid
      };
    } catch (error) {
      this.stats.totalErrors++;
      console.error('[AdvancedPool] Update execution error:', error);
      throw error;
    } finally {
      if (connection && this.pool) {
        await this.pool.release(connection);
      }
    }
  }

  async transaction<T>(callback: (conn: any) => Promise<T>): Promise<T> {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    const startTime = Date.now();
    let connection: PoolConnection | null = null;

    try {
      connection = await this.pool.acquire();
      connection.lastUsed = Date.now();
      connection.queryCount++;

      // Start transaction
      const transaction = connection.db.transaction(callback);
      const result = await transaction(connection.db);

      const responseTime = Date.now() - startTime;
      this.updateStats(responseTime);

      return result;
    } catch (error) {
      this.stats.totalErrors++;
      console.error('[AdvancedPool] Transaction error:', error);
      throw error;
    } finally {
      if (connection && this.pool) {
        await this.pool.release(connection);
      }
    }
  }

  getPoolStats(): PoolStats & typeof this.stats {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    return {
      size: this.pool.size,
      available: this.pool.available,
      borrowed: this.pool.borrowed,
      invalid: this.pool.invalid,
      pending: this.pool.pending,
      max: this.pool.max,
      min: this.pool.min,
      ...this.stats
    };
  }

  private updateStats(responseTime: number): void {
    this.stats.totalQueries++;
    this.stats.responseTimes.push(responseTime);

    // Keep only last 1000 response times
    if (this.stats.responseTimes.length > 1000) {
      this.stats.responseTimes = this.stats.responseTimes.slice(-1000);
    }

    // Calculate average response time
    this.stats.averageResponseTime = 
      this.stats.responseTimes.reduce((a, b) => a + b, 0) / this.stats.responseTimes.length;
  }

  async warmupConnections(): Promise<void> {
    if (!this.pool) {
      throw new Error('Pool not initialized');
    }

    console.log('[AdvancedPool] Warming up connections...');
    
    const connections: PoolConnection[] = [];
    
    try {
      // Create and test minimum number of connections
      for (let i = 0; i < this.pool.min; i++) {
        const connection = await this.pool.acquire();
        
        // Test the connection with a simple query
        connection.db.prepare('SELECT 1 as test').get();
        
        connections.push(connection);
      }
      
      console.log(`[AdvancedPool] Warmed up ${connections.length} connections`);
    } finally {
      // Release all connections back to the pool
      for (const connection of connections) {
        await this.pool.release(connection);
      }
    }
  }

  async shutdown(): Promise<void> {
    if (this.pool) {
      console.log('[AdvancedPool] Shutting down connection pool...');
      await this.pool.drain();
      await this.pool.clear();
      this.pool = null;
      this.isInitialized = false;
      console.log('[AdvancedPool] Pool shutdown complete');
    }
  }

  // Performance monitoring
  getPerformanceMetrics() {
    const stats = this.getPoolStats();
    const responseTimePercentiles = this.calculatePercentiles(this.stats.responseTimes);
    
    return {
      pool: {
        utilization: ((stats.borrowed / stats.max) * 100).toFixed(2) + '%',
        efficiency: ((stats.totalQueries / Math.max(stats.totalConnections, 1))).toFixed(2),
        errorRate: ((stats.totalErrors / Math.max(stats.totalQueries, 1)) * 100).toFixed(2) + '%'
      },
      performance: {
        averageResponseTime: stats.averageResponseTime.toFixed(2) + 'ms',
        p50: responseTimePercentiles.p50.toFixed(2) + 'ms',
        p95: responseTimePercentiles.p95.toFixed(2) + 'ms',
        p99: responseTimePercentiles.p99.toFixed(2) + 'ms'
      },
      health: {
        status: this.getHealthStatus(),
        uptime: this.isInitialized ? Date.now() - this.stats.totalConnections : 0
      }
    };
  }

  private calculatePercentiles(values: number[]) {
    if (values.length === 0) {
      return { p50: 0, p95: 0, p99: 0 };
    }

    const sorted = [...values].sort((a, b) => a - b);
    const len = sorted.length;

    return {
      p50: sorted[Math.floor(len * 0.5)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)]
    };
  }

  private getHealthStatus(): 'healthy' | 'warning' | 'critical' {
    const stats = this.getPoolStats();
    const errorRate = (stats.totalErrors / Math.max(stats.totalQueries, 1)) * 100;
    const utilization = (stats.borrowed / stats.max) * 100;

    if (errorRate > 5 || utilization > 90) {
      return 'critical';
    } else if (errorRate > 1 || utilization > 70) {
      return 'warning';
    }
    
    return 'healthy';
  }
}

// Export singleton instance
export const advancedPool = AdvancedDatabasePool.getInstance();