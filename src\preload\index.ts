/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @file index.ts
 * @description Preload script for securely exposing IPC communication to renderer
 */

import { contextBridge, ipc<PERSON>enderer } from 'electron';
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../shared/types/color.types';
import { SharedFolderFile } from '../shared/types/shared-folder';
import { SyncConfig, SyncConflict } from '../shared/types/sync.types';
import { TestDataChannels } from '../shared/constants/channels';

// Expose process info and APIs for debugging and window control
contextBridge.exposeInMainWorld('electron', {
  process: {
    versions: {
      node: process.versions.node,
      chrome: process.versions.chrome,
      electron: process.versions.electron
    },
    platform: process.platform,
    arch: process.arch
  },
  // Add window control methods
  window: {
    minimize: () => ipcRenderer.send('window:minimize'),
    maximize: () => ipcRenderer.send('window:maximize'),
    unmaximize: () => ipcRenderer.send('window:unmaximize'),
    close: () => ipcRenderer.send('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized'),
    toggleDevTools: () => ipcRenderer.invoke('window:toggleDevTools')
  },
  // IPC renderer APIs for specific modules
  ipcRenderer: {
    invoke: (channel: string, ...args: any[]) => {
      // Whitelist specific channels
      const validChannels = [
        'datasheet:open',
        'setup:getAppFolder',
        'setup:setAppFolder',
        'setup:isInitialSetupDone',
        'setup:completeInitialSetup',
        'setup:getSharedFolderPath',
        'setup:setSharedFolderPath',
        'test:echo'
      ];
      if (validChannels.includes(channel)) {
        console.log(`Preload: ipc.invoke called for channel "${channel}"`, args);
        return ipcRenderer.invoke(channel, ...args);
      } else {
        console.error(`Preload: ipc.invoke called for invalid channel "${channel}"`, args);
        throw new Error(`Invalid IPC channel: ${channel}`);
      }
    }
  }
});

// Expose secure IPC channel for the renderer
contextBridge.exposeInMainWorld('ipc', {
  invoke: (channel: string, ...args: any[]) => {
    // Whitelist valid channel patterns
    const validChannelPatterns = [
      /^selection:/,     // All selection-related channels
      /^color:/,         // All color-related channels
      /^product:/,       // All product-related channels
      /^datasheet:/,     // All datasheet-related channels
      /^shared-folder:/,  // All shared folder operations
      /^sync:/,          // All sync-related channels
      /^oauth:/,         // All OAuth-related channels
      /^test-data:/,     // All test data operations
      /^color-space-3d:/,// 3D color space window operations
      /^log-error$/,     // Error logging channel
      /^ping$/,          // Health check ping
    ];

    const isValidChannel = validChannelPatterns.some(pattern =>
      pattern.test(channel)
    );

    if (isValidChannel) {
      console.log(`Preload: window.ipc.invoke called for channel "${channel}"`, args);
      return ipcRenderer.invoke(channel, ...args);
    } else {
      console.error(`Preload: window.ipc.invoke rejected invalid channel "${channel}"`, args);
      throw new Error(`Invalid IPC channel: ${channel}`);
    }
  },
  on: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'color-space-3d:update-colors'
    ];
    
    if (validChannels.includes(channel)) {
      const listener = (_: any, ...args: any[]) => callback(...args);
      ipcRenderer.on(channel, listener);
      return () => ipcRenderer.removeListener(channel, listener);
    } else {
      throw new Error(`Invalid IPC listener channel: ${channel}`);
    }
  }
});

// Expose app API for general application functions
contextBridge.exposeInMainWorld('app', {
  // Zoom functionality
  zoom: {
    zoomIn: () => ipcRenderer.send('app:zoom-in'),
    zoomOut: () => ipcRenderer.send('app:zoom-out'),
    resetZoom: () => ipcRenderer.send('app:zoom-reset'),
    getZoomFactor: () => ipcRenderer.invoke('app:get-zoom-factor')
  },

  // Keyboard shortcuts info (for displaying in UI if needed)
  shortcuts: {
    zoomIn: process.platform === 'darwin' ? 'Cmd+Plus' : 'Ctrl+Plus',
    zoomOut: process.platform === 'darwin' ? 'Cmd+Minus' : 'Ctrl+Minus',
    resetZoom: process.platform === 'darwin' ? 'Cmd+0' : 'Ctrl+0'
  }
});

// Expose setup API for first-run configuration
contextBridge.exposeInMainWorld('setupAPI', {
  getInitialConfigStatus: () => ipcRenderer.invoke('get-initial-config-status'),
  selectSharedFolder: () => ipcRenderer.invoke('select-shared-folder'),
  saveStorageConfig: (config: { mode: 'standalone' | 'collaboration' | 'server-sync', path?: string }) => ipcRenderer.invoke('save-storage-config', config),
  // Listen for signal from main process to show the modal
  onShowSetupModal: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on('show-setup-modal', listener);
    return () => ipcRenderer.removeListener('show-setup-modal', listener);
  },
  // Signal main process that setup is complete
  sendSetupComplete: () => ipcRenderer.send('setup-complete')
});

// Expose product datasheet API and general API
contextBridge.exposeInMainWorld('api', {
  openProductDatasheet: (product: string) => {
    console.log(`Preload: calling api.openProductDatasheet for ${product}`);
    return ipcRenderer.invoke('product:open-datasheet', product);
  },
  // General invoke method for allowed channels
  invoke: (channel: string, ...args: any[]) => {
    const allowedChannels = [
      'test:create-product',
      'product:open-datasheet'
    ];
    
    if (allowedChannels.includes(channel)) {
      console.log(`Preload: api.invoke called for channel "${channel}"`, args);
      return ipcRenderer.invoke(channel, ...args);
    } else {
      console.error(`Preload: api.invoke rejected invalid channel "${channel}"`);
      throw new Error(`Invalid API channel: ${channel}`);
    }
  },
  // Event listener methods
  on: (channel: string, callback: Function) => {
    const allowedChannels = ['auth-complete', 'handle-invitation'];
    if (allowedChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },
  off: (channel: string, callback: Function) => {
    const allowedChannels = ['auth-complete', 'handle-invitation'];
    if (allowedChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback);
    }
  }
});

// Expose license API
contextBridge.exposeInMainWorld('licenseAPI', {
  // Get device status
  getStatus: async () => {
    console.log('Preload: calling licenseAPI.getStatus');
    return ipcRenderer.invoke('license:get-status');
  },

  // Activate device
  activateDevice: async () => {
    console.log('Preload: calling licenseAPI.activateDevice');
    return ipcRenderer.invoke('license:activate-device');
  },

  // Deactivate device
  deactivateDevice: async () => {
    console.log('Preload: calling licenseAPI.deactivateDevice');
    return ipcRenderer.invoke('license:deactivate-device');
  },

  // Check device authorization
  checkAuthorization: async () => {
    console.log('Preload: calling licenseAPI.checkAuthorization');
    return ipcRenderer.invoke('license:check');
  },

  // Show activation dialog
  showActivationDialog: async () => {
    console.log('Preload: calling licenseAPI.showActivationDialog');
    return ipcRenderer.invoke('license:show-activation-dialog');
  },

  // Listen for activation dialog events
  onShowActivationDialog: (callback: (data: any) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('license:show-activation-dialog', listener);
    return () => {
      ipcRenderer.removeListener('license:show-activation-dialog', listener);
    };
  },

  // Listen for license dialog events (used in App.tsx)
  onShowDialog: (callback: (data: any) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('license:show-dialog', listener);
    return () => {
      ipcRenderer.removeListener('license:show-dialog', listener);
    };
  },

  // Activate license with automatic key
  activateLicense: async () => {
    console.log('Preload: calling licenseAPI.activateLicense');
    return ipcRenderer.invoke('license:activate-license');
  },

  // Get device ID
  getDeviceId: async () => {
    console.log('Preload: calling licenseAPI.getDeviceId');
    return ipcRenderer.invoke('license:get-device-id');
  },

  // Get device license key
  getDeviceLicenseKey: async () => {
    console.log('Preload: calling licenseAPI.getDeviceLicenseKey');
    return ipcRenderer.invoke('license:get-device-license-key');
  }
});

// Expose shared folder API
contextBridge.exposeInMainWorld('sharedFolder', {
  // Get the shared folder path
  getPath: () => {
    console.log('Preload: calling sharedFolder.getPath');
    return ipcRenderer.invoke('shared-folder:get-path');
  },

  // Ensure the shared folder exists
  ensureExists: () => {
    console.log('Preload: calling sharedFolder.ensureExists');
    return ipcRenderer.invoke('shared-folder:ensure-exists');
  },

  // Read a file from the shared folder
  readFile: (fileName: string) => {
    console.log(`Preload: calling sharedFolder.readFile for ${fileName}`);
    return ipcRenderer.invoke('shared-folder:read-file', fileName);
  },

  // Write a file to the shared folder
  writeFile: (fileName: string, content: string) => {
    console.log(`Preload: calling sharedFolder.writeFile for ${fileName}`);
    return ipcRenderer.invoke('shared-folder:write-file', fileName, content);
  },

  // List files in the shared folder
  listFiles: () => {
    console.log('Preload: calling sharedFolder.listFiles');
    return ipcRenderer.invoke('shared-folder:list-files') as Promise<SharedFolderFile[]>;
  }
});

// Expose monitoring and update APIs
contextBridge.exposeInMainWorld('monitoring', {
  // Error tracking
  trackError: (error: Error | { message: string; stack?: string; componentStack?: string; location?: string }) => {
    console.log('Preload: tracking error through IPC');

    // Normalize error object
    const errorInfo = error instanceof Error
      ? {
          message: error.message,
          stack: error.stack
        }
      : error;

    return ipcRenderer.invoke('track-renderer-error', errorInfo);
  }
});

// Expose auto-update API
contextBridge.exposeInMainWorld('autoUpdate', {
  // Listen for auto-update status changes
  onStatusChange: (callback: (status: {
    status: 'checking' | 'available' | 'not-available' | 'downloading' | 'downloaded' | 'error';
    data?: any;
    error?: string;
  }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('update-event', listener);

    // Return function to remove listener
    return () => {
      ipcRenderer.removeListener('update-event', listener);
    };
  },

  // Check for updates manually
  checkForUpdates: () => {
    return ipcRenderer.invoke('update:check-for-updates');
  },

  // Download an available update
  downloadUpdate: () => {
    return ipcRenderer.invoke('update:download-update');
  },

  // Install a downloaded update
  installUpdate: () => {
    return ipcRenderer.invoke('update:install-update');
  }
});

// Typed API for color operations
contextBridge.exposeInMainWorld('colorAPI', {
  // Get all colors
  getAll: () => {
    console.log('Preload: calling colorAPI.getAll');
    return ipcRenderer.invoke(ColorChannels.GET_ALL);
  },

  // Get color usage counts
  getUsageCounts: () => {
    console.log('Preload: calling colorAPI.getUsageCounts');
    return ipcRenderer.invoke(ColorChannels.GET_USAGE_COUNTS);
  },

  // Get color by ID
  getById: (id: string) => {
    console.log(`Preload: calling colorAPI.getById for ${id}`);
    return ipcRenderer.invoke(ColorChannels.GET_BY_ID, id);
  },

  // Add new color
  add: (color: NewColorEntry) => {
    console.log(`Preload: calling colorAPI.add for ${color.name} ${color.code}`);
    return ipcRenderer.invoke(ColorChannels.ADD, color);
  },

  // Update color
  update: (id: string, updates: UpdateColorEntry) => {
    console.log(`Preload: calling colorAPI.update for ${id}`);
    return ipcRenderer.invoke(ColorChannels.UPDATE, id, updates);
  },

  // Delete color
  delete: (id: string) => {
    console.log(`Preload: calling colorAPI.delete for ${id}`);
    return ipcRenderer.invoke(ColorChannels.DELETE, id);
  },

  // Clear all colors
  clearAll: () => {
    console.log('Preload: calling colorAPI.clearAll');
    return ipcRenderer.invoke(ColorChannels.CLEAR_ALL);
  },

  // Import colors from JSON file
  importColors: (mergeMode?: 'replace' | 'merge', filePath?: string, format?: 'json' | 'csv') => {
    console.log(`Preload: calling colorAPI.importColors with channel "${ColorChannels.IMPORT}" (format: ${format || 'json'})`);
    try {
      // Check if channel is registered by main process
      return ipcRenderer.invoke(ColorChannels.IMPORT, mergeMode, filePath, format)
        .then(result => {
          console.log(`Import result from main:`, result);
          return result;
        })
        .catch(error => {
          console.error(`Import error from main:`, error);
          throw error;
        });
    } catch (error) {
      console.error(`Error calling importColors:`, error);
      throw error;
    }
  },

  // Export colors to file (now supports JSON and CSV formats)
  exportColors: (filePath?: string, format?: 'json' | 'csv') => {
    console.log(`Preload: calling colorAPI.exportColors with channel "${ColorChannels.EXPORT}" (format: ${format || 'json'})`);
    try {
      // Check if channel is registered by main process
      return ipcRenderer.invoke(ColorChannels.EXPORT, filePath, format)
        .then(result => {
          console.log(`Export result from main:`, result);
          return result;
        })
        .catch(error => {
          console.error(`Export error from main:`, error);
          throw error;
        });
    } catch (error) {
      console.error(`Error calling exportColors:`, error);
      throw error;
    }
  },

  // Normalize all Pantone codes in the database to a consistent format
  normalizePantoneCodes: () => {
    console.log(`Preload: calling colorAPI.normalizePantoneCodes`);
    try {
      // This feature is not implemented yet
      return Promise.resolve({ success: true, message: 'Feature not implemented' });
    } catch (error) {
      console.error(`Error calling normalizePantoneCodes:`, error);
      throw error;
    }
  }
});

// Selection API removed - now using direct product-color relationships

// Typed API for sync operations
contextBridge.exposeInMainWorld('syncAPI', {
  // Get sync configuration
  getConfig: async () => {
    console.log('Preload: calling syncAPI.getConfig');
    return ipcRenderer.invoke('sync:get-config');
  },

  // Check for unsynced local changes
  hasUnsyncedLocalChanges: async () => {
    console.log('Preload: calling syncAPI.hasUnsyncedLocalChanges');
    return ipcRenderer.invoke('sync:has-unsynced-local-changes');
  },

  // Update sync configuration
  updateConfig: async (config: Partial<SyncConfig>) => {
    console.log('Preload: calling syncAPI.updateConfig');
    return ipcRenderer.invoke('sync:update-config', config);
  },

  // Get sync state (for automatic sync without login)
  getState: async () => {
    console.log('Preload: calling syncAPI.getState');
    try {
      // Try new API first
      return await ipcRenderer.invoke('sync:get-state');
    } catch (_error) {
      // Fall back to the old API if the new one fails
      console.log('Falling back to legacy sync API');
      return ipcRenderer.invoke('sync:get-state');
    }
  },

  // Get authentication state
  getAuthState: async () => {
    console.log('Preload: calling syncAPI.getAuthState');
    return ipcRenderer.invoke('sync:get-auth-state');
  },

  // Login with Google (new Supabase implementation)
  login: async () => {
    console.log('Preload: calling syncAPI.login (Google OAuth)');
    return ipcRenderer.invoke('sync:login');
  },

  // Accept GDPR consent
  acceptGDPR: async (ip?: string) => {
    console.log('Preload: calling syncAPI.acceptGDPR');
    return ipcRenderer.invoke('sync:accept-gdpr', ip);
  },

  // Export user data (GDPR)
  exportData: async () => {
    console.log('Preload: calling syncAPI.exportData');
    return ipcRenderer.invoke('sync:export-data');
  },

  // Delete account and data (GDPR)
  deleteAccount: async () => {
    console.log('Preload: calling syncAPI.deleteAccount');
    return ipcRenderer.invoke('sync:delete-account');
  },

  // Legacy signup method (for backward compatibility)
  signup: async (email: string, password: string) => {
    console.log('Preload: signup not supported in Google-only mode');
    return { success: false, error: 'Please use Google sign-in' };
  },

  // Logout
  logout: async () => {
    console.log('Preload: calling syncAPI.logout');
    return ipcRenderer.invoke('sync:logout');
  },

  // Sync data
  syncData: async () => {
    console.log('Preload: calling syncAPI.syncData');
    return ipcRenderer.invoke('sync:sync');
  },

  // Manual sync
  sync: async () => {
    console.log('Preload: calling syncAPI.sync');
    return ipcRenderer.invoke('sync:sync');
  },

  // Initialize sync
  initialize: async () => {
    console.log('Preload: calling syncAPI.initialize');
    return ipcRenderer.invoke('sync:initialize');
  },

  testConnection: async () => {
    console.log('Preload: calling syncAPI.testConnection');
  },

  subscribe: async () => {
    console.log('Preload: calling syncAPI.subscribe');
  },

  unsubscribe: async () => {
    console.log('Preload: calling syncAPI.unsubscribe');
  },

  // Resolve conflicts
  resolveConflicts: async (resolutions: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>) => {
    console.log('Preload: calling syncAPI.resolveConflicts');
    return ipcRenderer.invoke('sync:resolve-conflicts', resolutions);
  },

  // Listen for sync status updates
  onStatusUpdate: (callback: (status: {
    status: 'idle' | 'syncing' | 'error' | 'success';
    message?: string;
  }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('sync:status-update', listener);
    return () => {
      ipcRenderer.removeListener('sync:status-update', listener);
    };
  },

  onDataChanged: (callback: (data: {
    table: string;
    event: string;
    data: any;
  }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('sync:data-changed', listener);
    return () => {
      ipcRenderer.removeListener('sync:data-changed', listener);
    };
  },

  // Listen for conflicts
  onConflicts: (callback: (conflicts: SyncConflict[]) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('sync:conflicts', listener);
    return () => {
      ipcRenderer.removeListener('sync:conflicts', listener);
    };
  },

  // OAuth configuration
  configureOAuth: async (settings: {
    authTimeout?: number;
    sessionTimeout?: number;
    sessionWarningTime?: number;
    autoLogoutEnabled?: boolean;
  }) => {
    console.log('Preload: calling syncAPI.configureOAuth');
    return ipcRenderer.invoke('oauth:configure', settings);
  },

  getOAuthConfig: async () => {
    console.log('Preload: calling syncAPI.getOAuthConfig');
    return ipcRenderer.invoke('oauth:get-config');
  },

  updateActivity: async () => {
    console.log('Preload: calling syncAPI.updateActivity');
    return ipcRenderer.invoke('oauth:update-activity');
  },

  resetAuthLoop: async () => {
    console.log('Preload: calling syncAPI.resetAuthLoop');
    return ipcRenderer.invoke('oauth:reset-auth-loop');
  },

  recoverAuth: async () => {
    console.log('Preload: calling syncAPI.recoverAuth');
    return ipcRenderer.invoke('oauth:recover-auth');
  },

  checkAuthHealth: async () => {
    console.log('Preload: calling syncAPI.checkAuthHealth');
    return ipcRenderer.invoke('oauth:check-health');
  },

  // Listen for session warnings and expiry
  onSessionWarning: (callback: (data: { minutesRemaining: number }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('auth:session-warning', listener);
    return () => {
      ipcRenderer.removeListener('auth:session-warning', listener);
    };
  },

  onSessionExpired: (callback: (data: { reason: string; timeout: number }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('auth:session-expired', listener);
    return () => {
      ipcRenderer.removeListener('auth:session-expired', listener);
    };
  },

  onAuthLoopDetected: (callback: (data: { 
    cooldownMinutes: number; 
    cooldownUntil: number; 
    attempts: number; 
    windowMinutes: number; 
  }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('auth:loop-detected', listener);
    return () => {
      ipcRenderer.removeListener('auth:loop-detected', listener);
    };
  },

  onAuthLoopReset: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on('auth:loop-reset', listener);
    return () => {
      ipcRenderer.removeListener('auth:loop-reset', listener);
    };
  }
});

// Typed API for organization operations
contextBridge.exposeInMainWorld('organizationAPI', {
  // Create new organization
  createOrganization: (data: { name: string; slug?: string }) => {
    console.log('Preload: calling organizationAPI.createOrganization');
    return ipcRenderer.invoke('organization:create', data);
  },

  // Get user's organizations
  getOrganizations: () => {
    console.log('Preload: calling organizationAPI.getOrganizations');
    return ipcRenderer.invoke('organization:getAll');
  },

  // Get current organization
  getCurrentOrganization: () => {
    console.log('Preload: calling organizationAPI.getCurrentOrganization');
    return ipcRenderer.invoke('organization:getCurrent');
  },

  // Set current organization
  setCurrentOrganization: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.setCurrentOrganization');
    return ipcRenderer.invoke('organization:setCurrent', organizationId);
  },

  // Get organization members
  getMembers: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.getMembers');
    return ipcRenderer.invoke('organization:getMembers', organizationId);
  },

  // Invite member
  inviteMember: (data: { organizationId: string; email: string; role?: string }) => {
    console.log('Preload: calling organizationAPI.inviteMember');
    return ipcRenderer.invoke('organization:inviteMember', data);
  },

  // Remove member
  removeMember: (data: { organizationId: string; userId: string }) => {
    console.log('Preload: calling organizationAPI.removeMember');
    return ipcRenderer.invoke('organization:removeMember', data);
  },

  // Update member role
  updateMemberRole: (data: { organizationId: string; userId: string; role: string }) => {
    console.log('Preload: calling organizationAPI.updateMemberRole');
    return ipcRenderer.invoke('organization:updateMemberRole', data);
  },

  // Update organization settings
  updateSettings: (data: { organizationId: string; settings: any }) => {
    console.log('Preload: calling organizationAPI.updateSettings');
    return ipcRenderer.invoke('organization:updateSettings', data);
  },

  // Delete organization
  deleteOrganization: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.deleteOrganization');
    return ipcRenderer.invoke('organization:delete', organizationId);
  },
  
  // Accept invitation
  acceptInvitation: (token: string) => {
    console.log('Preload: calling organizationAPI.acceptInvitation');
    return ipcRenderer.invoke('organization:acceptInvitation', token);
  },
  
  // Get pending invitations
  getPendingInvitations: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.getPendingInvitations');
    return ipcRenderer.invoke('organization:getPendingInvitations', organizationId);
  },
  
  // Revoke invitation
  revokeInvitation: (data: { organizationId: string; invitationId: string }) => {
    console.log('Preload: calling organizationAPI.revokeInvitation');
    return ipcRenderer.invoke('organization:revokeInvitation', data);
  },
  
  // Listen for invitation links
  onInvitationReceived: (callback: (token: string) => void) => {
    const listener = (_: any, token: string) => callback(token);
    ipcRenderer.on('handle-invitation', listener);
    return () => {
      ipcRenderer.removeListener('handle-invitation', listener);
    };
  }
});

// Typed API for test data operations
contextBridge.exposeInMainWorld('testDataAPI', {
  // Create test product with flat and gradient colors
  createTestProduct: () => {
    console.log('Preload: calling testDataAPI.createTestProduct');
    return ipcRenderer.invoke(TestDataChannels.CREATE_TEST_PRODUCT);
  },

  // Remove all test data
  removeTestData: () => {
    console.log('Preload: calling testDataAPI.removeTestData');
    return ipcRenderer.invoke(TestDataChannels.REMOVE_TEST_DATA);
  }
});

// Expose IPC event listener for handling invitation deep links
contextBridge.exposeInMainWorld('invitationHandler', {
  onInvitation: (callback: (token: string) => void) => {
    ipcRenderer.on('handle-invitation', (_event, token) => {
      callback(token);
    });
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeAllListeners('handle-invitation');
    };
  }
});
