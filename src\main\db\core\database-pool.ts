/**
 * Database connection pooling for better concurrent access
 */

import { getDatabasePath } from '../config';

export class DatabasePool {
  private static instance: DatabasePool;
  private connections: any[] = [];
  private readonly maxConnections = 5;
  private busyConnections = new Set<any>();
  private Database: any;
  
  private constructor() {}
  
  static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool();
    }
    return DatabasePool.instance;
  }
  
  async getConnection(): Promise<any> {
    // Return available connection
    const availableConnection = this.connections.find(conn => !this.busyConnections.has(conn));
    if (availableConnection) {
      this.busyConnections.add(availableConnection);
      return availableConnection;
    }
    
    // Create new connection if under limit
    if (this.connections.length < this.maxConnections) {
      const newConnection = this.createConnection();
      this.connections.push(newConnection);
      this.busyConnections.add(newConnection);
      return newConnection;
    }
    
    // Wait for connection to become available
    return new Promise((resolve) => {
      const checkForConnection = () => {
        const conn = this.connections.find(c => !this.busyConnections.has(c));
        if (conn) {
          this.busyConnections.add(conn);
          resolve(conn);
        } else {
          setTimeout(checkForConnection, 10);
        }
      };
      checkForConnection();
    });
  }
  
  releaseConnection(connection: any): void {
    this.busyConnections.delete(connection);
  }
  
  private createConnection(): any {
    if (!this.Database) {
      this.Database = this.loadBetterSqlite3();
    }
    const dbPath = getDatabasePath();
    return new this.Database(dbPath, { 
      verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
      fileMustExist: false
    });
  }
  
  private loadBetterSqlite3(): any {
    // Use window.require in Electron which bypasses webpack
    const electronRequire = (globalThis as any).require || require;
    return electronRequire('better-sqlite3');
  }
  
  closeAll(): void {
    this.connections.forEach(conn => {
      try {
        conn.close();
      } catch (error) {
        console.warn('Error closing database connection:', error);
      }
    });
    this.connections = [];
    this.busyConnections.clear();
  }
}

/**
 * Get database connection from pool
 */
export async function getPooledConnection(): Promise<any> {
  const pool = DatabasePool.getInstance();
  return await pool.getConnection();
}

/**
 * Release database connection back to pool
 */
export function releasePooledConnection(connection: any): void {
  const pool = DatabasePool.getInstance();
  pool.releaseConnection(connection);
}

/**
 * Execute a query with automatic connection pooling
 */
export async function executeWithPool<T>(operation: (db: any) => T): Promise<T> {
  const connection = await getPooledConnection();
  try {
    return operation(connection);
  } finally {
    releasePooledConnection(connection);
  }
}