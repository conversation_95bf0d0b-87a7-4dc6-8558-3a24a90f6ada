/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @file electron.d.ts
 * @description TypeScript declarations for Electron IPC
 */

import { ColorEntry, NewColorEntry, UpdateColorEntry } from './shared/types/color.types';

interface ImportResult {
  imported: boolean;
  count?: number;
  message: string;
}

interface ExportResult {
  exported: boolean;
  count?: number;
  message: string;
}

interface ColorAPI {
  getAll: () => Promise<ColorEntry[]>;
  getUsageCounts: () => Promise<Record<string, { count: number; products: string[] }>>;
  getById: (id: string) => Promise<ColorEntry | undefined>;
  add: (color: NewColorEntry) => Promise<ColorEntry>;
  update: (id: string, updates: UpdateColorEntry) => Promise<ColorEntry>;
  delete: (id: string) => Promise<boolean>;
  clearAll: () => Promise<boolean>;
  importColors: (mergeMode?: 'replace' | 'merge', filePath?: string) => Promise<ImportResult>;
  exportColors: (filePath?: string, format?: 'json' | 'csv') => Promise<ExportResult>;
}

interface IpcAPI {
  invoke: (channel: string, ...args: unknown[]) => Promise<unknown>;
}

interface ProcessVersions {
  node: string;
  chrome: string;
  electron: string;
}

interface ElectronProcess {
  versions: ProcessVersions;
  platform: string;
  arch: string;
  env: Record<string, string>;
}

interface Electron {
  process: ElectronProcess;
}

interface SyncAPI {
  testConnection: () => Promise<{ success: boolean; message?: string }>;
  getState: () => Promise<{ success: boolean; data: { lastSyncTime?: number } }>;
  onStatusUpdate: (callback: (data: unknown) => void) => void;
  offStatusUpdate: (callback: (data: unknown) => void) => void;
  hasUnsyncedLocalChanges: () => Promise<boolean>;
  syncData: () => Promise<{ success: boolean; timestamp?: number; error?: string }>;
}

interface AppZoomAPI {
  getZoomFactor: () => Promise<number>;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
}

interface AppShortcuts {
  zoomIn?: string;
  zoomOut?: string;
}

interface AppAPI {
  zoom?: AppZoomAPI;
  shortcuts?: AppShortcuts;
}

declare global {
  interface Window {
    colorAPI: ColorAPI;
    ipc: IpcAPI;
    electron?: Electron;
    syncAPI?: SyncAPI;
    app?: AppAPI;
  }
}
