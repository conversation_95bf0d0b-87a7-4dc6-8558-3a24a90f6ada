/**
 * @file sync-export-import.ts
 * @description Utilities for exporting and importing database data for synchronization
 * Enhanced implementation with proper error handling and conflict resolution
 */

// Import our custom better-sqlite3 helper instead of direct import
import { createDatabase } from './better-sqlite3-helper.js';
import type { Database as DatabaseType } from 'better-sqlite3';

import { getDatabasePath } from './config.js';

const DB_TABLES = {
  COLORS: 'colors',
  PRODUCTS: 'products',
  PRODUCT_COLORS: 'product_colors',
  DATASHEETS: 'datasheets',
  SYNC_METADATA: 'syncMetadata',
  DELETED_PRODUCTS: 'deletedProducts',
  DELETED_DATASHEETS: 'deletedDatasheets'
};

// Column mapping between camelCase (JS) and snake_case (DB)
const COLUMN_MAPPING: Record<string, string> = {
  // Base properties
  'id': 'id',
  'externalId': 'external_id',
  'createdAt': 'created_at',
  'updatedAt': 'updated_at',
  'createdBy': 'created_by',
  'updatedBy': 'updated_by',
  'isActive': 'is_active',
  'deleted': 'is_deleted',
  
  // Color properties
  'isLibrary': 'is_library',
  'legacyId': 'legacy_id',
  
  // Product-color properties
  'productId': 'product_id',
  'colorId': 'color_id',
  'addedAt': 'added_at',
  
  // Datasheet properties
  'filePath': 'file_path',
  'fileType': 'file_type',
  
  // Sync metadata properties
  'deviceId': 'device_id',
  'lastSync': 'last_sync',
  'syncVersion': 'sync_version'
};

// Reverse mapping (DB to JS)
const REVERSE_COLUMN_MAPPING: Record<string, string> = {};
Object.entries(COLUMN_MAPPING).forEach(([jsKey, dbKey]) => {
  REVERSE_COLUMN_MAPPING[dbKey] = jsKey;
});

import crypto from 'crypto';
import { app } from 'electron';

// Type definitions needed for export/import operations
export interface BaseRow {
  id: string | number;
  external_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  is_active?: number;
  // Support for camelCase versions (from transformation)
  updatedAt?: string;
  createdAt?: string;
}

export interface ColorRow extends BaseRow {
  product?: string;
  flavour?: string;
  pantone?: string;
  hex?: string;
  cmyk?: string;
  notes?: string;
  gradient?: string | null;
  is_library?: number;
  // Legacy fields
  name?: string;
  codeode?: string;
  ral_code?: string;
}

export interface ProductRow extends BaseRow {
  name: string;
  metadata?: string; // JSON string
}

export interface ProductColorRow {
  product_id: number;
  color_id: number;
  added_at?: string;
}

export interface DatasheetRow extends BaseRow {
  name: string;
  description?: string;
  file_path?: string;
  file_type?: string;
}

export interface ExportData {
  deviceId: string;
  timestamp: number;
  version: number;
  tables: Record<string, unknown[]>;
}

// Enhanced types for conflict resolution
export interface SyncConflict {
  table: string;
  localRecord: BaseRow;
  remoteRecord: BaseRow;
  resolution?: 'local' | 'remote' | 'merge';
}

// Sync result interface for better reporting
export interface SyncResult {
  success: boolean;
  timestamp: number;
  stats: Record<string, number>;
  conflicts: SyncConflict[];
  errors: string[];
}

// Sync metadata for tracking sync state
export interface SyncMetadata {
  lastSyncTimestamp: number;
  lastSyncDeviceId: string;
  syncVersion: number;
}

/**
 * Get a unique device ID for synchronization
 * @returns A unique device identifier
 */
const getDeviceId = (): string => {
  try {
    // Create a deterministic device ID based on user data path
    const userDataPath = app.getPath('userData');
    const hash = crypto.createHash('sha256').update(userDataPath).digest('hex');
    return `chromasync-${hash.substring(0, 12)}`;
  } catch (error: unknown) {
    console.error('[DB] Failed to get device ID:', error);
    // Use random ID as fallback but still make it somewhat unique
    return `default-${Math.floor(Math.random() * 1000000)}`;
  }
};

/**
 * Detects conflicts between local and remote records
 * @param localRecord Local database record
 * @param remoteRecord Remote record from sync
 * @returns Boolean indicating if conflict exists
 */
const detectConflict = (localRecord: BaseRow, remoteRecord: BaseRow): boolean => {
  // Check if both records have valid timestamps
  const localUpdated = localRecord.updated_at || localRecord.updatedAt;
  const remoteUpdated = remoteRecord.updated_at || remoteRecord.updatedAt;
  
  if (!localUpdated || !remoteUpdated) {return false;}
  
  const localDate = new Date(localUpdated).getTime();
  const remoteDate = new Date(remoteUpdated).getTime();
  
  // Consider a conflict if both records were updated since last sync
  // and have different timestamps (indicating independent updates)
  if (Math.abs(localDate - remoteDate) > 1000) {
    // More than 1 second difference between updates
    return true;
  }
  
  return false;
}

/**
 * Resolve conflict using defined strategy
 * @param conflict The detected conflict information
 * @returns Resolved record to be saved
 */
const resolveConflict = (conflict: SyncConflict): BaseRow => {
  // If resolution strategy is explicitly specified, follow it
  if (conflict.resolution === 'local') {return conflict.localRecord;}
  if (conflict.resolution === 'remote') {return conflict.remoteRecord;}
  
  // Default strategy: most recent wins
  const localUpdated = conflict.localRecord.updated_at || conflict.localRecord.updatedAt;
  const remoteUpdated = conflict.remoteRecord.updated_at || conflict.remoteRecord.updatedAt;
  
  const localDate = new Date(localUpdated || '').getTime();
  const remoteDate = new Date(remoteUpdated || '').getTime();
  
  return remoteDate > localDate ? conflict.remoteRecord : conflict.localRecord;
}

/**
 * Transforms a database row object with snake_case keys to camelCase JS properties
 * @param row Database row with snake_case keys
 * @returns Object with camelCase keys
 */
function transformDatabaseRowToJs(row: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  // Process each property in the row
  for (const [key, value] of Object.entries(row)) {
    // If we have a mapping for this key, use it, otherwise keep the original key
    const jsKey = REVERSE_COLUMN_MAPPING[key] || key;
    result[jsKey] = value;
  }
  
  return result;
}

/**
 * Transforms a JS object with camelCase properties to snake_case DB column names
 * @param obj JS object with camelCase properties
 * @returns Object with snake_case keys ready for database
 */
function transformJsToDatabase(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  // Process each property in the object
  for (const [key, value] of Object.entries(obj)) {
    // If we have a mapping for this key, use it, otherwise keep the original key
    const dbKey = COLUMN_MAPPING[key] || key;
    result[dbKey] = value;
  }
  
  return result;
}

/**
 * Validates and updates local schema to match expected structure
 * @param db Database instance
 * @returns Boolean indicating success
 */
const validateSchema = (db: DatabaseType): boolean => {
  try {
    if (!db) {
      console.error('[DB] validateSchema called with null database');
      return false;
    }
    
    // Check if required tables exist, create them if they don't
    const tables = [
      { name: 'colors', createSql: `
        CREATE TABLE IF NOT EXISTS colors (
          id INTEGER PRIMARY KEY,
          external_id TEXT UNIQUE,
          product TEXT,
          flavour TEXT,
          pantone TEXT,
          hex TEXT,
          cmyk TEXT,
          notes TEXT,
          gradient TEXT,
          is_library INTEGER DEFAULT 0,
          created_at TEXT,
          updated_at TEXT,
          created_by TEXT,
          updated_by TEXT,
          is_active INTEGER DEFAULT 1,
          name TEXT,
          codeode TEXT,
          ral_code TEXT
        )
      `},
      { name: 'products', createSql: `
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY,
          external_id TEXT UNIQUE,
          name TEXT NOT NULL,
          metadata TEXT,
          created_at TEXT,
          updated_at TEXT,
          created_by TEXT,
          updated_by TEXT,
          is_active INTEGER DEFAULT 1
        )
      `},
      { name: 'product_colors', createSql: `
        CREATE TABLE IF NOT EXISTS product_colors (
          product_id INTEGER NOT NULL,
          color_id INTEGER NOT NULL,
          added_at TEXT,
          PRIMARY KEY (product_id, color_id),
          FOREIGN KEY (product_id) REFERENCES products(id),
          FOREIGN KEY (color_id) REFERENCES colors(id)
        )
      `},
      { name: 'datasheets', createSql: `
        CREATE TABLE IF NOT EXISTS datasheets (
          id INTEGER PRIMARY KEY,
          external_id TEXT UNIQUE,
          name TEXT NOT NULL,
          description TEXT,
          file_path TEXT,
          file_type TEXT,
          created_at TEXT,
          updated_at TEXT,
          created_by TEXT,
          updated_by TEXT,
          is_active INTEGER DEFAULT 1
        )
      `}
    ];
    
    for (const table of tables) {
      const tableExists = db.prepare(
        `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
      ).get(table.name);
      
      if (!tableExists) {
        console.log(`[DB] Table ${table.name} doesn't exist, creating it...`);
        // Create the table using the SQL statement
        db.prepare(table.createSql).run();
        console.log(`[DB] Created ${table.name} table`);
      } else {
        // Table exists, check if it has all required columns
        console.log(`[DB] Table ${table.name} exists`);
      }
    }
    
    // Create indexes to improve query performance
    try {
      // Only run if db is available
      if (db) {
        // Index for product colors
        db.prepare(`CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id)`).run();
        db.prepare(`CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id)`).run();
        
        // Index for soft-deleted records
        db.prepare(`CREATE INDEX IF NOT EXISTS idx_colors_active ON colors(is_active)`).run();
        db.prepare(`CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)`).run();
      }
    } catch (indexError) {
      // Non-fatal, just log the error
      console.warn('[DB] Error creating indexes:', indexError);
    }
    
    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Schema validation failed:', errorMessage);
    return false;
  }
}

/**
 * Export database for synchronization
 * @returns Promise resolving to JSON data for sync
 */
export async function exportDatabase(): Promise<ExportData> {
  let db: DatabaseType | null = null;
  
  try {
    // Get database path
    const dbPath = getDatabasePath();

    // Open database connection (readonly) using our helper
    db = createDatabase(dbPath, { readonly: true });

    // Prepare export data structure with device-specific information
    const exportData: ExportData = {
      deviceId: getDeviceId(),
      timestamp: Date.now(),
      version: 1,
      tables: {}
    };

    // Export colors table with proper error handling
    try {
      if (!db) {throw new Error('Database not initialized');}
      
      const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get('colors');
      if (tableExists) {
        const colorRows = db.prepare(`SELECT * FROM colors WHERE is_active = 1`).all() as ColorRow[];
        // Transform DB column names to JS property names
        const transformedRows = colorRows.map(transformDatabaseRowToJs);
        exportData.tables[DB_TABLES.COLORS] = transformedRows;
        console.log(`[DB] Exported ${colorRows.length} colors`);
      } else {
        exportData.tables[DB_TABLES.COLORS] = [];
        console.log('[DB] Colors table does not exist, skipping export');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('[DB] Error exporting colors table:', errorMessage);
      exportData.tables[DB_TABLES.COLORS] = [];
    }

    // Export products table with proper error handling
    try {
      if (!db) {throw new Error('Database not initialized');}
      
      const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get('products');
      if (tableExists) {
        const productsRows = db.prepare(`SELECT * FROM products WHERE is_active = 1`).all() as ProductRow[];
        // Transform DB column names to JS property names
        const transformedRows = productsRows.map(transformDatabaseRowToJs);
        exportData.tables[DB_TABLES.PRODUCTS] = transformedRows;
        console.log(`[DB] Exported ${productsRows.length} products`);
      } else {
        exportData.tables[DB_TABLES.PRODUCTS] = [];
        console.log('[DB] Products table does not exist, skipping export');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('[DB] Error exporting products table:', errorMessage);
      exportData.tables[DB_TABLES.PRODUCTS] = [];
    }

    // Export product_colors relationships
    try {
      if (!db) {throw new Error('Database not initialized');}
      
      const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get('product_colors');
      if (tableExists) {
        const productColorsRows = db.prepare(`SELECT * FROM product_colors`).all() as ProductColorRow[];
        exportData.tables[DB_TABLES.PRODUCT_COLORS] = productColorsRows;
        console.log(`[DB] Exported ${productColorsRows.length} product-color relationships`);
      } else {
        exportData.tables[DB_TABLES.PRODUCT_COLORS] = [];
        console.log('[DB] Product_colors table does not exist, skipping export');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('[DB] Error exporting product_colors table:', errorMessage);
      exportData.tables[DB_TABLES.PRODUCT_COLORS] = [];
    }

    // Export datasheets table with proper error handling
    try {
      if (!db) {throw new Error('Database not initialized');}
      
      const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get('datasheets');
      if (tableExists) {
        const datasheetsRows = db.prepare(`SELECT * FROM datasheets WHERE is_active = 1`).all() as DatasheetRow[];
        // Transform DB column names to JS property names
        const transformedRows = datasheetsRows.map(transformDatabaseRowToJs);
        exportData.tables[DB_TABLES.DATASHEETS] = transformedRows;
        console.log(`[DB] Exported ${datasheetsRows.length} datasheets`);
      } else {
        exportData.tables[DB_TABLES.DATASHEETS] = [];
        console.log('[DB] Datasheets table does not exist, skipping export');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('[DB] Error exporting datasheets table:', errorMessage);
      exportData.tables[DB_TABLES.DATASHEETS] = [];
    }

    console.log('[DB] Database export completed successfully');
    return exportData;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Export database error:', errorMessage);
    // Return empty but valid export data on error
    return {
      deviceId: getDeviceId(),
      timestamp: Date.now(),
      version: 1,
      tables: {}
    };
  } finally {
    // Ensure database is always closed, even if an error occurs
    if (db) {
      try {
        db.close();
      } catch (closeError) {
        console.error('[DB] Error closing database connection:', closeError);
      }
    }
  }
}

/**
 * Import database from synchronization data
 * @param importData Data to import into the database
 * @returns Promise resolving to detailed import results
 */
export async function importDatabase(importData: ExportData): Promise<SyncResult> {
  let db: DatabaseType | null = null;
  const result: SyncResult = {
    success: false,
    timestamp: Date.now(),
    stats: {
      colors: 0,
      products: 0,
      product_colors: 0,
      datasheets: 0,
    },
    conflicts: [],
    errors: []
  };
  
  try {
    // Validate import data
    if (!importData || !importData.tables) {
      throw new Error('Invalid import data format');
    }

    // Get database path
    const dbPath = getDatabasePath();

    // Open database connection using our helper
    db = createDatabase(dbPath);

    // Validate and update schema if needed
    if (!db || !validateSchema(db)) {
      throw new Error('Failed to validate database schema');
    }

    try {
      // Begin transaction
      db.prepare('BEGIN TRANSACTION').run();
      
      // Get the device ID for metadata tracking
      const localDeviceId = getDeviceId();
      console.log(`[DB] Import running on device: ${localDeviceId}`);
      
      // Store metadata about this sync operation
      if (db) {
        try {
          // Check if sync_metadata table exists, create if needed
          const metadataExists = db.prepare(
            `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
          ).get('sync_metadata');
          
          if (!metadataExists) {
            db.prepare(`
              CREATE TABLE IF NOT EXISTS sync_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                last_sync_timestamp INTEGER NOT NULL,
                last_sync_device_id TEXT NOT NULL,
                sync_version INTEGER DEFAULT 1
              )
            `).run();
          }
          
          // Update sync metadata
          db.prepare(`
            INSERT INTO sync_metadata (last_sync_timestamp, last_sync_device_id, sync_version)
            VALUES (?, ?, ?)
          `).run(Date.now(), localDeviceId, 1);
        } catch (metaError: unknown) {
          // Non-fatal, log but continue
          const errorMessage = metaError instanceof Error ? metaError.message : 'Unknown error';
          console.warn('[DB] Error updating sync metadata:', errorMessage);
        }
      }
      
      // Helper function to check if a table exists
      const tableExists = (tableName: string): boolean => {
        try {
          if (!db) {throw new Error('Database not initialized');}
          
          const result = db.prepare(
            `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
          ).get(tableName);
          return !!result;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[DB] Error checking if table ${tableName} exists:`, errorMessage);
          return false;
        }
      };

      // Helper function to check if a record exists
      const recordExists = (tableName: string, id: string | number): BaseRow | null => {
        try {
          if (!db) {throw new Error('Database not initialized');}
          
          const idColumn = typeof id === 'string' ? 'external_id' : 'id';
          const result = db.prepare(`SELECT * FROM ${tableName} WHERE ${idColumn} = ?`).get(id) as Record<string, any> | undefined;
          
          if (!result) {return null;}
          
          // Transform from DB row to JS object
          return transformDatabaseRowToJs(result) as BaseRow;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[DB] Error checking if record exists in ${tableName}:`, errorMessage);
          return null;
        }
      };

      // Helper function to insert a new record
      const insertRecord = (tableName: string, record: BaseRow): boolean => {
        try {
          if (!db) {throw new Error('Database not initialized');}
          
          // Transform JS property names to DB column names
          const dbRecord = transformJsToDatabase(record);
          
          // Dynamically build insert statement based on record properties
          const columns = Object.keys(dbRecord).filter(key => dbRecord[key] !== undefined);
          const placeholders = columns.map(() => '?').join(', ');
          const values = columns.map(col => dbRecord[col]);
          
          const insertStmt = db.prepare(
            `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`
          );
          
          insertStmt.run(...values);
          return true;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[DB] Error inserting record into ${tableName}:`, errorMessage);
          return false;
        }
      };

      // Helper function to update an existing record
      const updateRecord = (tableName: string, record: BaseRow): boolean => {
        try {
          if (!db) {throw new Error('Database not initialized');}
          
          // Transform JS property names to DB column names
          const dbRecord = transformJsToDatabase(record);
          
          // Determine which ID to use for the WHERE clause
          const idColumn = dbRecord.external_id ? 'external_id' : 'id';
          const idValue = dbRecord.external_id || dbRecord.id;
          
          // Exclude id columns from update columns
          const columns = Object.keys(dbRecord)
            .filter(key => key !== 'id' && key !== 'external_id' && dbRecord[key] !== undefined);
            
          if (columns.length === 0) {return true;} // Nothing to update
          
          const setClause = columns.map(col => `${col} = ?`).join(', ');
          const values = [...columns.map(col => dbRecord[col]), idValue];
          
          const updateStmt = db.prepare(
            `UPDATE ${tableName} SET ${setClause} WHERE ${idColumn} = ?`
          );
          
          updateStmt.run(...values);
          return true;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[DB] Error updating record in ${tableName}:`, errorMessage);
          return false;
        }
      };

      // Process colors table
      if (importData.tables[DB_TABLES.COLORS] && Array.isArray(importData.tables[DB_TABLES.COLORS])) {
        const colors = importData.tables[DB_TABLES.COLORS] as ColorRow[];
        if (tableExists('colors')) {
          for (const color of colors) {
            try {
              const colorId = color.external_id || color.id;
              const existingColor = recordExists('colors', colorId);
              
              if (!existingColor) {
                // New record, insert it
                if (insertRecord('colors', color)) {
                  result.stats.colors++;
                }
              } else {
                // Check for conflicts
                if (detectConflict(existingColor, color)) {
                  // Add to conflicts list
                  const conflict: SyncConflict = {
                    table: 'colors',
                    localRecord: existingColor,
                    remoteRecord: color,
                    resolution: 'remote' // Default to remote wins
                  };
                  
                  result.conflicts.push(conflict);
                  
                  // Apply conflict resolution
                  const resolvedRecord = resolveConflict(conflict);
                  if (updateRecord('colors', resolvedRecord)) {
                    result.stats.colors++;
                  }
                } else {
                  // No conflict, update if remote is newer
                  const localUpdated = existingColor.updated_at || existingColor.updatedAt;
                  const remoteUpdated = color.updated_at || color.updatedAt;
                  
                  const localDate = new Date(localUpdated || '').getTime();
                  const remoteDate = new Date(remoteUpdated || '').getTime();
                  
                  if (remoteDate > localDate) {
                    if (updateRecord('colors', color)) {
                      result.stats.colors++;
                    }
                  }
                }
              }
            } catch (err: unknown) {
              const errorMessage = err instanceof Error ? err.message : 'Unknown error';
              console.error(`[DB] Error processing color ${color.id}:`, errorMessage);
              result.errors.push(`Error processing color ${color.id}: ${errorMessage}`);
            }
          }
        } else {
          result.errors.push('Colors table does not exist');
        }
      }

      // Process products table
      if (importData.tables[DB_TABLES.PRODUCTS] && Array.isArray(importData.tables[DB_TABLES.PRODUCTS])) {
        const products = importData.tables[DB_TABLES.PRODUCTS] as ProductRow[];
        if (tableExists('products')) {
          for (const product of products) {
            try {
              const productId = product.external_id || product.id;
              const existingProduct = recordExists('products', productId);
              
              if (!existingProduct) {
                // New record, insert it
                if (insertRecord('products', product)) {
                  result.stats.products++;
                }
              } else {
                // Check for conflicts
                if (detectConflict(existingProduct, product)) {
                  // Add to conflicts list
                  const conflict: SyncConflict = {
                    table: 'products',
                    localRecord: existingProduct,
                    remoteRecord: product,
                    resolution: 'remote' // Default to remote wins
                  };
                  
                  result.conflicts.push(conflict);
                  
                  // Apply conflict resolution
                  const resolvedRecord = resolveConflict(conflict);
                  if (updateRecord('products', resolvedRecord)) {
                    result.stats.products++;
                  }
                } else {
                  // No conflict, update if remote is newer
                  const localUpdated = existingProduct.updated_at || existingProduct.updatedAt;
                  const remoteUpdated = product.updated_at || product.updatedAt;
                  
                  const localDate = new Date(localUpdated || '').getTime();
                  const remoteDate = new Date(remoteUpdated || '').getTime();
                  
                  if (remoteDate > localDate) {
                    if (updateRecord('products', product)) {
                      result.stats.products++;
                    }
                  }
                }
              }
            } catch (err: unknown) {
              const errorMessage = err instanceof Error ? err.message : 'Unknown error';
              console.error(`[DB] Error processing product ${product.id}:`, errorMessage);
              result.errors.push(`Error processing product ${product.id}: ${errorMessage}`);
            }
          }
        } else {
          result.errors.push('Products table does not exist');
        }
      }

      // Process product_colors relationships
      if (importData.tables[DB_TABLES.PRODUCT_COLORS] && Array.isArray(importData.tables[DB_TABLES.PRODUCT_COLORS])) {
        const productColors = importData.tables[DB_TABLES.PRODUCT_COLORS] as ProductColorRow[];
        if (tableExists('product_colors')) {
          for (const relation of productColors) {
            try {
              // Check if relationship exists
              const existingRelation = db?.prepare(
                `SELECT * FROM product_colors WHERE product_id = ? AND color_id = ?`
              ).get(relation.product_id, relation.color_id);
              
              if (!existingRelation) {
                // New relationship, insert it
                db?.prepare(
                  `INSERT INTO product_colors (product_id, color_id, added_at) VALUES (?, ?, ?)`
                ).run(relation.product_id, relation.color_id, relation.added_at || new Date().toISOString());
                result.stats.product_colors++;
              }
            } catch (err: unknown) {
              const errorMessage = err instanceof Error ? err.message : 'Unknown error';
              console.error(`[DB] Error processing product-color relationship:`, errorMessage);
              result.errors.push(`Error processing product-color relationship: ${errorMessage}`);
            }
          }
        } else {
          result.errors.push('Product_colors table does not exist');
        }
      }
      
      // Process datasheets
      if (importData.tables[DB_TABLES.DATASHEETS] && Array.isArray(importData.tables[DB_TABLES.DATASHEETS])) {
        const datasheets = importData.tables[DB_TABLES.DATASHEETS] as DatasheetRow[];
        if (tableExists('datasheets')) {
          for (const datasheet of datasheets) {
            try {
              const datasheetId = datasheet.external_id || datasheet.id;
              const existingDatasheet = recordExists('datasheets', datasheetId);
              
              if (!existingDatasheet) {
                // New record, insert it
                if (insertRecord('datasheets', datasheet)) {
                  result.stats.datasheets++;
                }
              } else {
                // Check for conflicts
                if (detectConflict(existingDatasheet, datasheet)) {
                  // Add to conflicts list
                  const conflict: SyncConflict = {
                    table: 'datasheets',
                    localRecord: existingDatasheet,
                    remoteRecord: datasheet,
                    resolution: 'remote' // Default to remote wins
                  };
                  
                  result.conflicts.push(conflict);
                  
                  // Apply conflict resolution
                  const resolvedRecord = resolveConflict(conflict);
                  if (updateRecord('datasheets', resolvedRecord)) {
                    result.stats.datasheets++;
                  }
                } else {
                  // No conflict, update if remote is newer
                  const localUpdated = existingDatasheet.updated_at || existingDatasheet.updatedAt;
                  const remoteUpdated = datasheet.updated_at || datasheet.updatedAt;
                  
                  const localDate = new Date(localUpdated || '').getTime();
                  const remoteDate = new Date(remoteUpdated || '').getTime();
                  
                  if (remoteDate > localDate) {
                    if (updateRecord('datasheets', datasheet)) {
                      result.stats.datasheets++;
                    }
                  }
                }
              }
            } catch (err: unknown) {
              const errorMessage = err instanceof Error ? err.message : 'Unknown error';
              console.error(`[DB] Error processing datasheet ${datasheet.id}:`, errorMessage);
              result.errors.push(`Error processing datasheet ${datasheet.id}: ${errorMessage}`);
            }
          }
        } else {
          result.errors.push('Datasheets table does not exist');
        }
      }

      // Commit transaction if we got this far
      if (db) {db.prepare('COMMIT').run();}
      
      result.success = true;
      console.log('[DB] Database import completed successfully', result.stats);
      return result;
    } catch (error: unknown) {
      // Rollback transaction on error
      if (db) {
        try {
          db.prepare('ROLLBACK').run();
        } catch (rollbackError: unknown) {
          const errorMessage = rollbackError instanceof Error ? rollbackError.message : 'Unknown error';
          console.error('[DB] Error during transaction rollback:', errorMessage);
        }
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[DB] Error during database import:', errorMessage);
      result.errors.push(`Import failed: ${errorMessage}`);
      
      return result;
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Import database error:', errorMessage);
    result.errors.push(`Database connection error: ${errorMessage}`);
    
    return result;
  } finally {
    // Ensure database is always closed, even if an error occurs
    if (db) {
      try {
        db.close();
      } catch (closeError) {
        console.error('[DB] Error closing database connection:', closeError);
      }
    }
  }
}