/**
 * Safe Migration Runner with validation and rollback capabilities
 */

import fs from 'fs';
import path from 'path';

export interface MigrationCheckpoint {
  migrationId: number;
  checkpointName: string;
  sqlStatement: string;
  timestamp: string;
}

export interface MigrationResult {
  success: boolean;
  migrationId: number;
  error?: Error;
  rollbackSuccessful?: boolean;
}

export class SafeMigrationRunner {
  private db: any;
  private checkpoints: MigrationCheckpoint[] = [];
  private appliedMigrations: Set<number> = new Set();
  
  constructor(database: any) {
    this.db = database;
    this.initializeMigrationTables();
    this.loadAppliedMigrations();
  }
  
  /**
   * Initialize migration tracking tables
   */
  private initializeMigrationTables(): void {
    // Create migrations table if not exists
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY,
        filename TEXT NOT NULL,
        applied_at TEXT DEFAULT CURRENT_TIMESTAMP,
        checksum TEXT,
        rollback_sql TEXT,
        status TEXT DEFAULT 'completed' CHECK (status IN ('completed', 'failed', 'rolled_back'))
      );
      
      CREATE TABLE IF NOT EXISTS migration_checkpoints (
        id INTEGER PRIMARY KEY,
        migration_id INTEGER REFERENCES migrations(id),
        checkpoint_name TEXT NOT NULL,
        sql_statement TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE IF NOT EXISTS migration_logs (
        id INTEGER PRIMARY KEY,
        migration_id INTEGER,
        level TEXT CHECK (level IN ('info', 'warning', 'error')),
        message TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }
  
  /**
   * Load previously applied migrations
   */
  private loadAppliedMigrations(): void {
    const applied = this.db.prepare(`
      SELECT id FROM migrations WHERE status = 'completed'
    `).all() as Array<{id: number}>;
    
    applied.forEach(m => this.appliedMigrations.add(m.id));
  }
  
  /**
   * Check if a column exists in a table
   */
  columnExists(tableName: string, columnName: string): boolean {
    const result = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM pragma_table_info(?) 
      WHERE name = ?
    `).get(tableName, columnName) as { count: number };
    
    return result.count > 0;
  }
  
  /**
   * Check if a table exists
   */
  tableExists(tableName: string): boolean {
    const result = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='table' AND name = ?
    `).get(tableName) as { count: number };
    
    return result.count > 0;
  }
  
  /**
   * Check if an index exists
   */
  indexExists(indexName: string): boolean {
    const result = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='index' AND name = ?
    `).get(indexName) as { count: number };
    
    return result.count > 0;
  }
  
  /**
   * Create a checkpoint for rollback
   */
  private createCheckpoint(migrationId: number, name: string, sql: string): void {
    const checkpoint: MigrationCheckpoint = {
      migrationId,
      checkpointName: name,
      sqlStatement: sql,
      timestamp: new Date().toISOString()
    };
    
    this.checkpoints.push(checkpoint);
    
    this.db.prepare(`
      INSERT INTO migration_checkpoints (migration_id, checkpoint_name, sql_statement)
      VALUES (?, ?, ?)
    `).run(migrationId, name, sql);
  }
  
  /**
   * Log migration activity
   */
  private log(migrationId: number, level: 'info' | 'warning' | 'error', message: string): void {
    console.log(`[Migration ${migrationId}] ${level.toUpperCase()}: ${message}`);
    
    this.db.prepare(`
      INSERT INTO migration_logs (migration_id, level, message)
      VALUES (?, ?, ?)
    `).run(migrationId, level, message);
  }
  
  /**
   * Validate migration before execution
   */
  private validateMigration(migrationId: number, sql: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for dangerous operations without existence checks
    const dangerousPatterns = [
      /ALTER\s+TABLE\s+(\w+)\s+ADD\s+COLUMN\s+(\w+)/gi,
      /CREATE\s+INDEX\s+(\w+)/gi,
      /CREATE\s+TABLE\s+(\w+)/gi
    ];
    
    for (const pattern of dangerousPatterns) {
      let match;
      while ((match = pattern.exec(sql)) !== null) {
        if (pattern.source.includes('ALTER')) {
          const [, tableName, columnName] = match;
          if (!sql.includes(`columnExists('${tableName}', '${columnName}')`)) {
            errors.push(`ALTER TABLE for ${tableName}.${columnName} without existence check`);
          }
        } else if (pattern.source.includes('INDEX')) {
          const [, indexName] = match;
          if (!sql.includes(`indexExists('${indexName}')`)) {
            errors.push(`CREATE INDEX for ${indexName} without existence check`);
          }
        } else if (pattern.source.includes('TABLE')) {
          const [, tableName] = match;
          if (!sql.includes(`tableExists('${tableName}')`)) {
            errors.push(`CREATE TABLE for ${tableName} without existence check`);
          }
        }
      }
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  /**
   * Run a single migration with safety checks
   */
  async runMigration(migrationId: number, filename: string, sql: string): Promise<MigrationResult> {
    // Skip if already applied
    if (this.appliedMigrations.has(migrationId)) {
      this.log(migrationId, 'info', 'Migration already applied, skipping');
      return { success: true, migrationId };
    }
    
    this.log(migrationId, 'info', `Starting migration: ${filename}`);
    
    // Validate migration
    const validation = this.validateMigration(migrationId, sql);
    if (!validation.valid) {
      validation.errors.forEach(error => {
        this.log(migrationId, 'warning', `Validation warning: ${error}`);
      });
    }
    
    // Create transaction savepoint
    const savepoint = `migration_${migrationId}`;
    
    try {
      // Start transaction
      this.db.exec(`SAVEPOINT ${savepoint}`);
      
      // Record migration start
      this.db.prepare(`
        INSERT INTO migrations (id, filename, status)
        VALUES (?, ?, 'in_progress')
      `).run(migrationId, filename);
      
      // Parse and execute SQL statements
      const statements = this.parseSqlStatements(sql);
      
      for (const statement of statements) {
        if (statement.trim()) {
          // Check for conditional execution
          const processedStatement = this.processConditionalStatement(statement);
          if (processedStatement) {
            this.createCheckpoint(migrationId, `statement_${statements.indexOf(statement)}`, statement);
            this.db.exec(processedStatement);
          }
        }
      }
      
      // Mark migration as completed
      this.db.prepare(`
        UPDATE migrations 
        SET status = 'completed', applied_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(migrationId);
      
      // Release savepoint
      this.db.exec(`RELEASE ${savepoint}`);
      
      this.log(migrationId, 'info', 'Migration completed successfully');
      this.appliedMigrations.add(migrationId);
      
      return { success: true, migrationId };
      
    } catch (error) {
      this.log(migrationId, 'error', `Migration failed: ${error.message}`);
      
      // Rollback to savepoint
      try {
        this.db.exec(`ROLLBACK TO ${savepoint}`);
        this.db.exec(`RELEASE ${savepoint}`);
        
        this.db.prepare(`
          UPDATE migrations 
          SET status = 'failed'
          WHERE id = ?
        `).run(migrationId);
        
        return { 
          success: false, 
          migrationId, 
          error: error as Error,
          rollbackSuccessful: true 
        };
      } catch (rollbackError) {
        this.log(migrationId, 'error', `Rollback failed: ${rollbackError.message}`);
        return { 
          success: false, 
          migrationId, 
          error: error as Error,
          rollbackSuccessful: false 
        };
      }
    }
  }
  
  /**
   * Parse SQL into individual statements
   */
  private parseSqlStatements(sql: string): string[] {
    // Simple parser - splits on semicolons not inside quotes
    const statements: string[] = [];
    let current = '';
    let inQuote = false;
    let quoteChar = '';
    
    for (let i = 0; i < sql.length; i++) {
      const char = sql[i];
      
      if ((char === '"' || char === "'") && sql[i - 1] !== '\\') {
        if (!inQuote) {
          inQuote = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuote = false;
        }
      }
      
      if (char === ';' && !inQuote) {
        statements.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    if (current.trim()) {
      statements.push(current.trim());
    }
    
    return statements;
  }
  
  /**
   * Process conditional statements (e.g., check column existence)
   */
  private processConditionalStatement(statement: string): string | null {
    // Handle ALTER TABLE ADD COLUMN with existence check
    const alterMatch = statement.match(/ALTER\s+TABLE\s+(\w+)\s+ADD\s+COLUMN\s+(\w+)\s+(.+)/i);
    if (alterMatch) {
      const [, tableName, columnName, columnDef] = alterMatch;
      if (this.columnExists(tableName, columnName)) {
        this.log(0, 'info', `Column ${tableName}.${columnName} already exists, skipping`);
        return null;
      }
      return statement;
    }
    
    // Handle CREATE INDEX with existence check
    const indexMatch = statement.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
    if (indexMatch && !statement.includes('IF NOT EXISTS')) {
      const [, indexName] = indexMatch;
      if (this.indexExists(indexName)) {
        this.log(0, 'info', `Index ${indexName} already exists, skipping`);
        return null;
      }
      return statement;
    }
    
    // Handle CREATE TABLE with existence check
    const tableMatch = statement.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
    if (tableMatch && !statement.includes('IF NOT EXISTS')) {
      const [, tableName] = tableMatch;
      if (this.tableExists(tableName)) {
        this.log(0, 'info', `Table ${tableName} already exists, skipping`);
        return null;
      }
      return statement;
    }
    
    return statement;
  }
  
  /**
   * Run all pending migrations
   */
  async runAllMigrations(migrationsPath: string): Promise<{ successful: number; failed: number }> {
    const files = fs.readdirSync(migrationsPath)
      .filter(f => f.endsWith('.sql'))
      .sort();
    
    let successful = 0;
    let failed = 0;
    
    for (const file of files) {
      const migrationId = parseInt(file.split('_')[0]);
      if (isNaN(migrationId)) continue;
      
      const filePath = path.join(migrationsPath, file);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      const result = await this.runMigration(migrationId, file, sql);
      
      if (result.success) {
        successful++;
      } else {
        failed++;
        // Stop on first failure
        break;
      }
    }
    
    return { successful, failed };
  }
  
  /**
   * Get migration status
   */
  getMigrationStatus(): any {
    return this.db.prepare(`
      SELECT 
        m.*,
        COUNT(mc.id) as checkpoint_count,
        COUNT(ml.id) as log_count
      FROM migrations m
      LEFT JOIN migration_checkpoints mc ON m.id = mc.migration_id
      LEFT JOIN migration_logs ml ON m.id = ml.migration_id
      GROUP BY m.id
      ORDER BY m.id
    `).all();
  }
  
  /**
   * Rollback a specific migration
   */
  async rollbackMigration(migrationId: number): Promise<boolean> {
    // This would need to be implemented with proper rollback SQL
    // For now, we just mark it as rolled back
    try {
      this.db.prepare(`
        UPDATE migrations 
        SET status = 'rolled_back'
        WHERE id = ?
      `).run(migrationId);
      
      this.appliedMigrations.delete(migrationId);
      return true;
    } catch (error) {
      console.error('Rollback failed:', error);
      return false;
    }
  }
}