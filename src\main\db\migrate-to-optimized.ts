/**
 * Migration script to convert old database.sqlite to new optimized schema
 */

import Database from 'better-sqlite3';
import path from 'path';
import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';

export async function migrateToOptimizedDatabase(userId?: string): Promise<{ success: boolean; error?: string; migratedColors: number; migratedProducts: number }> {
  const userDataPath = app.getPath('userData');
  const oldDbPath = path.join(userDataPath, 'database.sqlite');
  const newDbPath = path.join(userDataPath, 'chromasync.db');
  
  console.log('[Migration] Starting database migration...');
  console.log('[Migration] Old database:', oldDbPath);
  console.log('[Migration] New database:', newDbPath);
  
  // Check if old database exists
  if (!fs.existsSync(oldDbPath)) {
    console.log('[Migration] Old database not found, skipping migration');
    return { success: true, migratedColors: 0, migratedProducts: 0 };
  }
  
  try {
    // Open both databases
    const oldDb = new Database(oldDbPath, { readonly: true });
    const newDb = new Database(newDbPath);
    
    // Enable foreign keys
    newDb.pragma('foreign_keys = ON');
    
    // Create optimized schema if it doesn't exist
    const schemaPath = path.join(__dirname, '../../../scripts/create-optimized-schema.sql');
    if (fs.existsSync(schemaPath)) {
      const schema = fs.readFileSync(schemaPath, 'utf8');
      newDb.exec(schema);
    } else {
      // Fallback: create minimal schema
      newDb.exec(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY,
          external_id TEXT UNIQUE NOT NULL,
          user_id TEXT,
          name TEXT NOT NULL,
          sku TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          metadata JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS colors (
          id INTEGER PRIMARY KEY,
          external_id TEXT UNIQUE NOT NULL,
          user_id TEXT,
          source_id INTEGER DEFAULT 1,
          code TEXT NOT NULL,
          display_name TEXT,
          hex CHAR(7) NOT NULL,
          is_gradient BOOLEAN DEFAULT FALSE,
          properties JSON,
          deleted_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS product_colors (
          product_id INTEGER REFERENCES products(id),
          color_id INTEGER REFERENCES colors(id),
          display_order INTEGER DEFAULT 0,
          PRIMARY KEY (product_id, color_id)
        );
        
        CREATE TABLE IF NOT EXISTS color_cmyk (
          color_id INTEGER PRIMARY KEY REFERENCES colors(id),
          c REAL NOT NULL,
          m REAL NOT NULL,
          y REAL NOT NULL,
          k REAL NOT NULL
        );
        
        CREATE TABLE IF NOT EXISTS color_rgb (
          color_id INTEGER PRIMARY KEY REFERENCES colors(id),
          r INTEGER NOT NULL,
          g INTEGER NOT NULL,
          b INTEGER NOT NULL
        );
      `);
    }
    
    // Start transaction
    const migrationTransaction = newDb.transaction(() => {
      const migratedColors = 0;
      let migratedProducts = 0;
      
      // Migrate products
      const oldProducts = oldDb.prepare('SELECT DISTINCT product FROM colors').all();
      const productMap = new Map<string, number>();
      
      for (const row of oldProducts) {
        const productName = row.product as string;
        const external_id = uuidv4();
        
        const result = newDb.prepare(`
          INSERT INTO products (external_id, user_id, name, sku, metadata, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `).run(external_id, userId || null, productName, productName, JSON.stringify({}));
        
        productMap.set(productName, result.lastInsertRowid as number);
        migratedProducts++;
      }
      
      // Migrate colors
      const oldColors = oldDb.prepare('SELECT * FROM colors').all();
      
      for (const color of oldColors) {
        const external_id = uuidv4();
        
        // Parse CMYK values
        let cmykValues = { c: 0, m: 0, y: 0, k: 0 };
        if (color.cmyk) {
          const cmykMatch = color.cmyk.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
          if (cmykMatch) {
            cmykValues = {
              c: parseInt(cmykMatch[1]),
              m: parseInt(cmykMatch[2]),
              y: parseInt(cmykMatch[3]),
              k: parseInt(cmykMatch[4])
            };
          }
        }
        
        // Build properties JSON
        const properties: any = {};
        if (color.notes) {properties.notes = color.notes;}
        if (color.gradient) {properties.gradient = color.gradient;}
        if (color.is_library) {properties.is_library = color.is_library;}
        
        // Insert color
        const colorResult = newDb.prepare(`
          INSERT INTO colors (
            external_id, user_id, source_id, code, display_name, hex, 
            is_gradient, properties, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          external_id,
          userId || null,
          1, // default source_id
          color.colourCode || color.pantone || '', // handle both old column names
          color.uniqueId || color.flavour || '', // handle both old column names
          color.hex,
          color.gradient ? 1 : 0,
          JSON.stringify(properties),
          color.created_at,
          color.updated_at
        );
        
        const colorId = colorResult.lastInsertRowid as number;
        
        // Insert CMYK values
        newDb.prepare(`
          INSERT INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(colorId, cmykValues.c, cmykValues.m, cmykValues.y, cmykValues.k);
        
        // Calculate and insert RGB values from hex
        const hex = color.hex.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        
        newDb.prepare(`
          INSERT INTO color_rgb (color_id, r, g, b) 
          VALUES (?, ?, ?, ?)
        `).run(colorId, r, g, b);
        
        // LAB and HSL will be calculated on-demand
      }
      
      return newDb.prepare('SELECT COUNT(*) as count FROM colors').get().count;
    })();
    
    console.log(`✓ Migration complete! Migrated ${migratedCount} colors`);
    
    oldDb.close();
    newDb.close();
    
    return {
      success: true,
      migratedCount
    };
  } catch (error) {
    console.error('Migration failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
