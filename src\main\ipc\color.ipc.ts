/**
 * @file color.ipc.ts
 * @description IPC handlers for color operations
 */

import { ipcMain, dialog } from 'electron';
import { ColorService } from '../db/services/color.service';
import { ColorImportService } from '../db/services/color-import.service';
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../../shared/types/color.types';
import { getCurrentOrganizationId } from './organization.ipc';
import fs from 'fs';
import path from 'path';
import Database from 'better-sqlite3';

/**
 * Type for handling unknown errors
 */
type ErrorWithMessage = {
  message: string;
};

/**
 * Helper function to safely extract error message
 * @param error - Any error object
 * @returns Normalized error message 
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {return error.message;}
  if (typeof error === 'string') {return error;}
  if (isErrorWithMessage(error)) {return error.message;}
  return String(error);
}

/**
 * Type guard for objects with message property
 */
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

/**
 * Register IPC handlers for color operations
 */
export function registerColorHandlers(colorService: ColorService, db: Database.Database): void {
  // Get all colors
  ipcMain.handle(ColorChannels.GET_ALL, async () => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      return colorService.getAll(organizationId);
    } catch (error: unknown) {
      console.error('Error in color:getAll:', error);
      throw new Error(`Failed to get color entries: ${getErrorMessage(error)}`);
    }
  });

  // Get color usage counts
  ipcMain.handle(ColorChannels.GET_USAGE_COUNTS, async () => {
    try {
      const usageMap = colorService.getColorUsageCounts();
      // Convert Map to plain object for IPC
      const usageObject: Record<string, { count: number; products: string[] }> = {};
      usageMap.forEach((value, key) => {
        usageObject[key] = value;
      });
      return usageObject;
    } catch (error: unknown) {
      console.error('Error in color:getUsageCounts:', error);
      throw new Error(`Failed to get color usage counts: ${getErrorMessage(error)}`);
    }
  });

  // Get color by ID
  ipcMain.handle(ColorChannels.GET_BY_ID, async (_, id: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const color = colorService.getById(id, organizationId);
      if (!color) {
        throw new Error(`Color with ID ${id} not found`);
      }
      return color;
    } catch (error: unknown) {
      console.error(`Error in color:getById for ID ${id}:`, error);
      throw new Error(`Failed to get color entry: ${getErrorMessage(error)}`);
    }
  });

  // Add new color
  ipcMain.handle(ColorChannels.ADD, async (_, color: NewColorEntry) => {
    console.log('[IPC] color:add received:', JSON.stringify(color, null, 2));
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      // TODO: Get userId from auth context for audit trail
      const userId = undefined; // Will be implemented with auth
      return colorService.add(color, organizationId, userId);
    } catch (error: unknown) {
      console.error('Error in color:add:', error);
      throw new Error(`Failed to add color entry: ${getErrorMessage(error)}`);
    }
  });

  // Update color
  ipcMain.handle(ColorChannels.UPDATE, async (_, id: string, updates: UpdateColorEntry) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const updatedColor = colorService.update(id, updates, organizationId);
      if (!updatedColor) {
        throw new Error(`Color with ID ${id} not found`);
      }
      return updatedColor;
    } catch (error: unknown) {
      console.error(`Error in color:update for ID ${id}:`, error);
      throw new Error(`Failed to update color entry: ${getErrorMessage(error)}`);
    }
  });

  // Delete color
  ipcMain.handle(ColorChannels.DELETE, async (_, id: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const success = colorService.delete(id, organizationId);
      if (!success) {
        throw new Error(`Color with ID ${id} not found`);
      }
      return success;
    } catch (error: unknown) {
      console.error(`Error in color:delete for ID ${id}:`, error);
      throw new Error(`Failed to delete color entry: ${getErrorMessage(error)}`);
    }
  });

  // Clear all colors
  ipcMain.handle(ColorChannels.CLEAR_ALL, async () => {
    try {
      colorService.clearAll();
      return true;
    } catch (error: unknown) {
      console.error('Error in color:clearAll:', error);
      throw new Error(`Failed to clear color entries: ${getErrorMessage(error)}`);
    }
  });

  // Import colors from JSON file
  ipcMain.handle(ColorChannels.IMPORT, async (_, mergeMode?: 'replace' | 'merge') => {
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [{ name: 'JSON Files', extensions: ['json'] }]
      });
      
      if (canceled || filePaths.length === 0) {
        return { imported: false, message: 'Import cancelled' };
      }
      
      const filePath = filePaths[0];
      const data = fs.readFileSync(filePath, 'utf-8');
      const rawColors = JSON.parse(data);
      
      // Handle legacy field names (pantone -> code, flavour -> name)
      console.log(`[ColorImport] Processing ${rawColors.length} colors from import file`);
      
      // Log first color to see structure
      if (rawColors.length > 0) {
        console.log('[ColorImport] First color structure:', rawColors[0]);
      }
      
      const colors = rawColors.map((color: any, index: number) => {
        const migrated: NewColorEntry = {
          product: color.product,
          organizationId: color.organizationId || getCurrentOrganizationId() || '',  // Use from file or current org
          name: color.name || color.flavour || '',  // Support both new and old field names
          code: color.code || color.pantone || '',  // Support both new and old field names
          hex: color.hex,
          cmyk: color.cmyk,
          notes: color.notes,
          gradient: color.gradient
        };
        
        // Log if we're migrating old field names
        if (color.flavour || color.pantone) {
          console.log(`[ColorImport] Migrating legacy fields for color ${index + 1}:`, { 
            flavour: color.flavour, 
            pantone: color.pantone,
            to: { name: migrated.name, code: migrated.code }
          });
        }
        
        // Validate required fields
        if (!migrated.code || !migrated.hex) {
          console.error(`[ColorImport] Color ${index + 1} missing required fields:`, {
            original: color,
            migrated,
            missingCode: !migrated.code,
            missingHex: !migrated.hex
          });
        }
        
        return migrated;
      }) as NewColorEntry[];
      
      // If replace mode, hard delete all colors first
      if (mergeMode === 'replace') {
        console.log('[ColorImport] Replace mode: permanently deleting all existing colors');
        colorService.clearAll(true); // true = hard delete
      }
      
      // Use the new import service that handles product creation
      const colorImportService = new ColorImportService(db, colorService);
      const result = await colorImportService.importColorsWithProducts(colors, {
        createMissingProducts: true
      });
      
      const stats = colorImportService.getImportStats();
      
      return { 
        imported: true, 
        count: result.colors.length,
        productsCreated: result.products.length,
        errors: result.errors,
        message: `Successfully imported ${result.colors.length} colors${result.products.length > 0 ? ` and created ${result.products.length} products` : ''}`,
        stats
      };
    } catch (error: unknown) {
      console.error('Error in color:import:', error);
      throw new Error(`Failed to import colors: ${getErrorMessage(error)}`);
    }
  });

  // Export colors to JSON file
  ipcMain.handle(ColorChannels.EXPORT, async () => {
    try {
      const { canceled, filePath } = await dialog.showSaveDialog({
        filters: [{ name: 'JSON Files', extensions: ['json'] }]
      });
      
      if (canceled || !filePath) {
        return { exported: false, message: 'Export cancelled' };
      }
      
      const colors = colorService.getAll();
      const data = JSON.stringify(colors, null, 2);
      
      fs.writeFileSync(filePath, data, 'utf-8');
      return { 
        exported: true, 
        count: colors.length, 
        message: `Successfully exported ${colors.length} colors to ${path.basename(filePath)}` 
      };
    } catch (error: unknown) {
      console.error('Error in color:export:', error);
      throw new Error(`Failed to export colors: ${getErrorMessage(error)}`);
    }
  });
}