/**
 * @file color.ipc.ts
 * @description IPC handlers for color operations with enterprise-grade organization context validation
 * Refactored for 9.5+ rating standards with comprehensive error handling and validation
 */

import { ipcMain, dialog } from 'electron';
import { ColorService } from '../db/services/color.service';
import { ColorImportService } from '../db/services/color-import.service';
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../../shared/types/color.types';
import { getCurrentOrganizationId } from './organization.ipc';
import fs from 'fs';
import path from 'path';
import Database from 'better-sqlite3';

/**
 * Standard response interface for IPC operations
 */
interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
}

/**
 * Type for handling unknown errors
 */
type ErrorWithMessage = {
  message: string;
};

/**
 * Helper function to safely extract error message
 * @param error - Any error object
 * @returns Normalized error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {return error.message;}
  if (typeof error === 'string') {return error;}
  if (isErrorWithMessage(error)) {return error.message;}
  return String(error);
}

/**
 * Type guard for objects with message property
 */
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

/**
 * Create standardized success response
 */
function createSuccessResponse<T>(data: T, message?: string): IPCResponse<T> {
  return {
    success: true,
    data,
    userMessage: message,
    timestamp: Date.now()
  };
}

/**
 * Create standardized error response
 */
function createErrorResponse(error: string, userMessage?: string): IPCResponse {
  return {
    success: false,
    error,
    userMessage: userMessage || 'An error occurred while processing your request.',
    timestamp: Date.now()
  };
}

/**
 * Handle organization context validation errors
 */
function handleOrganizationError(error: OrganizationContextError): IPCResponse {
  return {
    success: false,
    error: error.error,
    userMessage: error.userMessage,
    timestamp: error.timestamp
  };
}

/**
 * Enhanced IPC handler wrapper with organization context validation
 */
function createSecureHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R> | R
) {
  return async (...args: T): Promise<IPCResponse<R> | OrganizationContextError> => {
    try {
      // Validate organization context
      const validation = await validateOrganizationContext();
      if (!validation.isValid && validation.error) {
        console.warn('[ColorIPC] Organization context validation failed:', validation.error);
        return validation.error;
      }

      // Execute handler with validated context
      const result = await handler(...args);

      // Return success response
      if (typeof result === 'object' && result !== null && 'success' in result) {
        // If result is already a response object, return as-is
        return result as IPCResponse<R>;
      }

      return createSuccessResponse(result);

    } catch (error) {
      console.error('[ColorIPC] Handler error:', error);
      const errorMessage = getErrorMessage(error);
      return createErrorResponse(
        errorMessage,
        'Unable to complete the operation. Please try again or contact support if the issue persists.'
      );
    }
  };
}

/**
 * Register IPC handlers for color operations
 */
export function registerColorHandlers(colorService: ColorService, db: Database.Database): void {
  // Get all colors
  ipcMain.handle(ColorChannels.GET_ALL, async () => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      return colorService.getAll(organizationId);
    } catch (error: unknown) {
      console.error('Error in color:getAll:', error);
      throw new Error(`Failed to get color entries: ${getErrorMessage(error)}`);
    }
  });

  // Get color usage counts
  ipcMain.handle(ColorChannels.GET_USAGE_COUNTS, createSecureHandler(async () => {
    const usageMap = colorService.getColorUsageCounts();

    // Convert Map to plain object for IPC
    const usageObject: Record<string, { count: number; products: string[] }> = {};
    usageMap.forEach((value, key) => {
      usageObject[key] = value;
    });

    console.log(`[ColorIPC] Retrieved usage counts for ${Object.keys(usageObject).length} colors`);

    return createSuccessResponse(usageObject, 'Color usage counts retrieved successfully');
  }));

  // Get color by ID
  ipcMain.handle(ColorChannels.GET_BY_ID, createSecureHandler(async (_, id: string) => {
    // Input validation
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new Error('Invalid color ID provided');
    }

    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    const color = colorService.getById(id.trim(), organizationId);
    if (!color) {
      throw new Error(`Color with ID ${id} not found in your organization`);
    }

    console.log(`[ColorIPC] Retrieved color ${id} for organization ${organizationId}`);

    return createSuccessResponse(color, 'Color retrieved successfully');
  }));

  // Add new color
  ipcMain.handle(ColorChannels.ADD, createSecureHandler(async (_, color: NewColorEntry) => {
    console.log('[ColorIPC] Adding new color:', JSON.stringify(color, null, 2));

    // Input validation
    if (!color || typeof color !== 'object') {
      throw new Error('Invalid color data provided');
    }

    if (!color.hex || !color.code) {
      throw new Error('Color hex and code are required fields');
    }

    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    // Get current user ID for audit trail
    const userId = getCurrentUserId();

    // Ensure organization ID is set on the color
    const colorWithOrg = { ...color, organizationId };

    const addedColor = colorService.add(colorWithOrg, organizationId, userId);

    console.log(`[ColorIPC] Successfully added color ${addedColor.id} for organization ${organizationId}`);

    return createSuccessResponse(addedColor, 'Color added successfully');
  }));

  // Update color
  ipcMain.handle(ColorChannels.UPDATE, createSecureHandler(async (_, id: string, updates: UpdateColorEntry) => {
    // Input validation
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new Error('Invalid color ID provided');
    }

    if (!updates || typeof updates !== 'object') {
      throw new Error('Invalid update data provided');
    }

    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    const updatedColor = colorService.update(id.trim(), updates, organizationId);
    if (!updatedColor) {
      throw new Error(`Color with ID ${id} not found in your organization`);
    }

    console.log(`[ColorIPC] Successfully updated color ${id} for organization ${organizationId}`);

    return createSuccessResponse(updatedColor, 'Color updated successfully');
  }));

  // Delete color
  ipcMain.handle(ColorChannels.DELETE, createSecureHandler(async (_, id: string) => {
    // Input validation
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new Error('Invalid color ID provided');
    }

    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    const success = colorService.delete(id.trim(), organizationId);
    if (!success) {
      throw new Error(`Color with ID ${id} not found in your organization`);
    }

    console.log(`[ColorIPC] Successfully deleted color ${id} for organization ${organizationId}`);

    return createSuccessResponse(true, 'Color deleted successfully');
  }));

  // Clear all colors
  ipcMain.handle(ColorChannels.CLEAR_ALL, createSecureHandler(async () => {
    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    // This is a destructive operation, so we should be extra careful
    console.warn(`[ColorIPC] Clearing all colors for organization ${organizationId}`);

    colorService.clearAll();

    console.log(`[ColorIPC] Successfully cleared all colors for organization ${organizationId}`);

    return createSuccessResponse(true, 'All colors cleared successfully');
  }));

  // Import colors from JSON file
  ipcMain.handle(ColorChannels.IMPORT, createSecureHandler(async (_, mergeMode?: 'replace' | 'merge') => {
    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    // Show file dialog
    const { canceled, filePaths } = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters: [{ name: 'JSON Files', extensions: ['json'] }]
    });

    if (canceled || filePaths.length === 0) {
      return createSuccessResponse({ imported: false, message: 'Import cancelled' });
    }

    const filePath = filePaths[0];

    // Validate file exists and is readable
    if (!fs.existsSync(filePath)) {
      throw new Error('Selected file does not exist');
    }

    let rawColors: any[];
    try {
      const data = fs.readFileSync(filePath, 'utf-8');
      rawColors = JSON.parse(data);
    } catch (error) {
      throw new Error(`Failed to read or parse JSON file: ${getErrorMessage(error)}`);
    }

    // Validate data structure
    if (!Array.isArray(rawColors)) {
      throw new Error('Import file must contain an array of colors');
    }

    if (rawColors.length === 0) {
      return createSuccessResponse({
        imported: true,
        count: 0,
        message: 'No colors found in import file'
      });
    }

    console.log(`[ColorIPC] Processing ${rawColors.length} colors from import file for organization ${organizationId}`);

    // Log first color to see structure
    if (rawColors.length > 0) {
      console.log('[ColorIPC] First color structure:', rawColors[0]);
    }

    // Process and validate colors
    const colors = rawColors.map((color: any, index: number) => {
      if (!color || typeof color !== 'object') {
        throw new Error(`Invalid color data at index ${index}`);
      }

      const migrated: NewColorEntry = {
        product: color.product,
        organizationId: organizationId, // Always use validated organization ID
        name: color.name || color.flavour || '',  // Support both new and old field names
        code: color.code || color.pantone || '',  // Support both new and old field names
        hex: color.hex,
        cmyk: color.cmyk,
        notes: color.notes,
        gradient: color.gradient
      };

      // Log if we're migrating old field names
      if (color.flavour || color.pantone) {
        console.log(`[ColorIPC] Migrating legacy fields for color ${index + 1}:`, {
          flavour: color.flavour,
          pantone: color.pantone,
          to: { name: migrated.name, code: migrated.code }
        });
      }

      // Validate required fields
      if (!migrated.code || !migrated.hex) {
        throw new Error(`Color at index ${index + 1} missing required fields (code: ${!!migrated.code}, hex: ${!!migrated.hex})`);
      }

      return migrated;
    }) as NewColorEntry[];

    // If replace mode, hard delete all colors first
    if (mergeMode === 'replace') {
      console.warn(`[ColorIPC] Replace mode: permanently deleting all existing colors for organization ${organizationId}`);
      colorService.clearAll(true); // true = hard delete
    }

    // Use the import service that handles product creation
    const colorImportService = new ColorImportService(db, colorService);
    const result = await colorImportService.importColorsWithProducts(colors, {
      createMissingProducts: true
    });

    const stats = colorImportService.getImportStats();

    const successMessage = `Successfully imported ${result.colors.length} colors${result.products.length > 0 ? ` and created ${result.products.length} products` : ''}`;

    console.log(`[ColorIPC] ${successMessage} for organization ${organizationId}`);

    return createSuccessResponse({
      imported: true,
      count: result.colors.length,
      productsCreated: result.products.length,
      errors: result.errors,
      message: successMessage,
      stats
    }, successMessage);
  }));

  // Export colors to JSON file
  ipcMain.handle(ColorChannels.EXPORT, createSecureHandler(async () => {
    const organizationId = await getValidatedOrganizationId();
    if (!organizationId) {
      throw new Error('Organization context validation failed');
    }

    // Show save dialog
    const { canceled, filePath } = await dialog.showSaveDialog({
      filters: [{ name: 'JSON Files', extensions: ['json'] }],
      defaultPath: `colors-${organizationId}-${new Date().toISOString().split('T')[0]}.json`
    });

    if (canceled || !filePath) {
      return createSuccessResponse({ exported: false, message: 'Export cancelled' });
    }

    // Get colors for the organization
    const colors = colorService.getAll(organizationId);

    if (colors.length === 0) {
      return createSuccessResponse({
        exported: true,
        count: 0,
        message: 'No colors to export for this organization'
      });
    }

    // Add export metadata
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        organizationId,
        version: '1.0',
        colorCount: colors.length
      },
      colors
    };

    try {
      const data = JSON.stringify(exportData, null, 2);
      fs.writeFileSync(filePath, data, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to write export file: ${getErrorMessage(error)}`);
    }

    const successMessage = `Successfully exported ${colors.length} colors to ${path.basename(filePath)}`;

    console.log(`[ColorIPC] ${successMessage} for organization ${organizationId}`);

    return createSuccessResponse({
      exported: true,
      count: colors.length,
      filePath: path.basename(filePath),
      message: successMessage
    }, successMessage);
  }));
}