/**
 * Optimized database implementation using the new schema
 * This replaces the old database.ts with the optimized schema
 */

import path from 'path';
import fs from 'fs';
import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import { createMigrationIntegration } from '../services/enhanced-realtime-sync.service';
import { getDatabasePath } from './config';
import { COMPLETE_SCHEMA } from './schemas/complete-schema';

import type { 
  ColorEntry
} from '../../shared/types/color.types';

// Import color utilities from consolidated module
import {
  isValidHex,
  hexToCmyk,
  hexToRgb,
  standardizeCMYK
} from '../../shared/utils/color';

// We'll load Database dynamically to avoid bundler issues
let Database: any;
let db: any = null;

// Connection pooling for better concurrent access
class DatabasePool {
  private static instance: DatabasePool;
  private connections: any[] = [];
  private readonly maxConnections = 5;
  private busyConnections = new Set<any>();
  
  private constructor() {}
  
  static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool();
    }
    return DatabasePool.instance;
  }
  
  async getConnection(): Promise<any> {
    // Return available connection
    const availableConnection = this.connections.find(conn => !this.busyConnections.has(conn));
    if (availableConnection) {
      this.busyConnections.add(availableConnection);
      return availableConnection;
    }
    
    // Create new connection if under limit
    if (this.connections.length < this.maxConnections) {
      const newConnection = this.createConnection();
      this.connections.push(newConnection);
      this.busyConnections.add(newConnection);
      return newConnection;
    }
    
    // Wait for connection to become available (simplified approach)
    return new Promise((resolve) => {
      const checkForConnection = () => {
        const conn = this.connections.find(c => !this.busyConnections.has(c));
        if (conn) {
          this.busyConnections.add(conn);
          resolve(conn);
        } else {
          setTimeout(checkForConnection, 10);
        }
      };
      checkForConnection();
    });
  }
  
  releaseConnection(connection: any): void {
    this.busyConnections.delete(connection);
  }
  
  private createConnection(): any {
    if (!Database) {
      Database = this.loadBetterSqlite3();
    }
    const dbPath = getDatabasePath();
    return new Database(dbPath, { 
      verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
      fileMustExist: false
    });
  }
  
  private loadBetterSqlite3(): any {
    // Use window.require in Electron which bypasses webpack
    const electronRequire = (globalThis as any).require || require;
    return electronRequire('better-sqlite3');
  }
  
  closeAll(): void {
    this.connections.forEach(conn => {
      try {
        conn.close();
      } catch (error) {
        console.warn('Error closing database connection:', error);
      }
    });
    this.connections = [];
    this.busyConnections.clear();
  }
}

// ID mapping cache for UUID <-> integer conversion
const idMappingCache = new Map<string, number>();
const reverseIdMappingCache = new Map<number, string>();

/**
 * Get the database path
 */
function getDatabasePath(): string {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'chromasync.db');
}

/**
 * Get database connection from pool
 */
export async function getPooledConnection(): Promise<any> {
  const pool = DatabasePool.getInstance();
  return await pool.getConnection();
}

/**
 * Release database connection back to pool
 */
export function releasePooledConnection(connection: any): void {
  const pool = DatabasePool.getInstance();
  pool.releaseConnection(connection);
}

/**
 * Execute a query with automatic connection pooling
 */
export async function executeWithPool<T>(operation: (db: any) => T): Promise<T> {
  const connection = await getPooledConnection();
  try {
    return operation(connection);
  } finally {
    releasePooledConnection(connection);
  }
}

/**
 * Initialize database with optimized schema and connection pooling
 */
export async function initDatabase(): Promise<any | null> {
  if (db) {return db;}

  // Inner function to load ID mappings
  function loadIdMappingsInternal(): void {
    if (!db) {return;}

    try {
      // Check if the external_id column exists
      const hasExternalId = db.prepare(`
        SELECT COUNT(*) as count 
        FROM pragma_table_info('products') 
        WHERE name = 'external_id'
      `).get() as { count: number };

      if (hasExternalId.count > 0) {
        // Load product mappings
        const products = db.prepare('SELECT id, external_id FROM products').all() as Array<{id: number, external_id: string}>;
        products.forEach(p => {
          idMappingCache.set(p.external_id, p.id);
          reverseIdMappingCache.set(p.id, p.external_id);
        });

        // Load color mappings
        const colors = db.prepare('SELECT id, external_id FROM colors').all() as Array<{id: number, external_id: string}>;
        colors.forEach(c => {
          idMappingCache.set(c.external_id, c.id);
          reverseIdMappingCache.set(c.id, c.external_id);
        });

        console.log(`[DB] Loaded ${idMappingCache.size} ID mappings`);
      } else {
        console.log('[DB] Database is using legacy schema without external_id column');
      }
    } catch (error) {
      console.error('[DB] Error loading ID mappings:', error);
    }
  }

  try {
    // Load better-sqlite3 module for Electron
    if (!Database) {
      const electronRequire = (globalThis as any).require || require;
      Database = electronRequire('better-sqlite3');
    }
    
    const dbPath = getDatabasePath();
    console.log(`[DB] Initializing optimized database at: ${dbPath}`);

    // Ensure directory exists
    const dirPath = path.dirname(dbPath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // Create database with optimal settings
    try {
      db = new Database(dbPath);
    } catch (err) {
      console.error('[DB] Failed to initialize database:', err);
      throw err;
    }
    
    // Apply optimal PRAGMA settings
    db.exec(`
      PRAGMA foreign_keys = ON;
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA temp_store = MEMORY;
      PRAGMA mmap_size = 30000000000;
      PRAGMA cache_size = -64000;
      PRAGMA wal_autocheckpoint = 1000;
    `);

    // Check if all required tables exist BEFORE running migrations
    const requiredTables = ['colors', 'products', 'organizations', 'organization_members', 'users'];
    const existingTables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN (${requiredTables.map(() => '?').join(',')})
    `).all(...requiredTables).map((row: any) => row.name);
    
    console.log('[DB] Existing tables:', existingTables);
    
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      console.log('[DB] Missing tables:', missingTables);
      
      // Always create complete schema if any core tables are missing
      if (missingTables.includes('colors') || missingTables.includes('products')) {
        console.log('[DB] Core tables missing, creating complete schema...');
        await createCompleteSchema();
      } else if (missingTables.includes('organizations') || missingTables.includes('organization_members') || missingTables.includes('users')) {
        console.log('[DB] Organization/user tables missing, creating them...');
        await ensureOrganizationTables();
      }
    } else {
      console.log('[DB] All required tables exist');
      
      // Verify table schemas are correct
      await verifyTableSchemas();
    }

    // Run migrations AFTER ensuring core schema exists
    // TODO: Temporarily disabled enterprise migration system to fix immediate DB loading issue
    // Will re-enable after fixing the async/sync issue
    try {
      console.log('[DB] Enterprise migration system temporarily disabled');
      // const migrationHelper = createMigrationIntegration();
      // const migrationSuccess = await migrationHelper.runEnhancedMigrations();

      // if (!migrationSuccess) {
      //   console.error('[DB] Enterprise migration failed - continuing with existing schema');
      //   // Don't throw here - allow the app to continue with existing schema
      // } else {
      //   console.log('[DB] Enterprise migration completed successfully');
      // }
    } catch (error) {
      console.error('[DB] Migration failed:', error);
      // Don't throw here - allow the app to continue with existing schema
    }

    // Load ID mappings
    loadIdMappingsInternal();
    
    return db;
  } catch (error) {
    console.error('[DB] Failed to initialize database:', error);
    db = null;
    return null;
  }
}

/**
 * Create the legacy schema for backward compatibility
 */
async function createLegacySchema(): Promise<void> {
  if (!db) {throw new Error('Database not initialized');}

  const schemaPath = path.join(__dirname, '..', '..', '..', 'schema.sql');
  console.log('[DB] Reading legacy schema from:', schemaPath);
  
  try {
    const schema = fs.readFileSync(schemaPath, 'utf8');
    db.exec(schema);
    console.log('[DB] Legacy schema created successfully');
  } catch (error) {
    console.error('[DB] Error creating legacy schema:', error);
    throw error;
  }
}

/**
 * Create the complete schema with all required tables
 */
async function createCompleteSchema(): Promise<void> {
  if (!db) {throw new Error('Database not initialized');}
  
  console.log('[DB] Creating complete schema with organization support...');
  
  try {
    db.exec(COMPLETE_SCHEMA);
    console.log('[DB] Complete schema created successfully');
  } catch (error) {
    console.error('[DB] Error creating complete schema:', error);
    throw error;
  }
}

/**
 * Ensure organization tables exist
 */
async function ensureOrganizationTables(): Promise<void> {
  if (!db) {throw new Error('Database not initialized');}
  
  console.log('[DB] Ensuring organization tables exist...');
  
  try {
    // Create organization tables if they don't exist
    db.exec(`
      -- Organizations table
      CREATE TABLE IF NOT EXISTS organizations (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
        settings JSON DEFAULT '{}',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      -- Organization members table
      CREATE TABLE IF NOT EXISTS organization_members (
        organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        user_id TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
        joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
        invited_by TEXT,
        PRIMARY KEY (organization_id, user_id)
      );

      -- Organization invitations table
      CREATE TABLE IF NOT EXISTS organization_invitations (
        id INTEGER PRIMARY KEY,
        organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        email TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
        invited_by TEXT NOT NULL,
        invited_at TEXT DEFAULT CURRENT_TIMESTAMP,
        expires_at TEXT NOT NULL,
        accepted_at TEXT,
        token TEXT UNIQUE NOT NULL,
        UNIQUE(organization_id, email)
      );

      -- Users table (local cache of user data)
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        avatar_url TEXT,
        preferences JSON DEFAULT '{}',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
      CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
      CREATE INDEX IF NOT EXISTS idx_organizations_external ON organizations(external_id);
      CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
      CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
      CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);

      -- Triggers for updated_at
      CREATE TRIGGER IF NOT EXISTS update_organizations_timestamp 
      AFTER UPDATE ON organizations
      BEGIN
        UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
      AFTER UPDATE ON users
      BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);
    
    console.log('[DB] Organization tables created successfully');
  } catch (error) {
    console.error('[DB] Error creating organization tables:', error);
    throw error;
  }
}

/**
 * Verify table schemas are correct
 */
async function verifyTableSchemas(): Promise<void> {
  if (!db) {throw new Error('Database not initialized');}
  
  console.log('[DB] Verifying table schemas...');
  
  try {
    // Check if users table has the correct schema
    const userColumns = db.prepare(`
      SELECT name FROM pragma_table_info('users')
    `).all().map((col: any) => col.name);
    
    console.log('[DB] Users table columns:', userColumns);
    
    // The OAuth service expects 'display_name', not 'name'
    if (!userColumns.includes('display_name') && userColumns.includes('name')) {
      console.log('[DB] Migrating users table schema...');
      db.exec(`
        -- Create new users table with correct schema
        CREATE TABLE IF NOT EXISTS users_new (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          display_name TEXT,
          avatar_url TEXT,
          preferences JSON DEFAULT '{}',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Copy data from old table if it exists
        INSERT OR IGNORE INTO users_new (id, email, display_name, avatar_url, preferences, created_at, updated_at)
        SELECT id, email, name as display_name, avatar_url, 
               CASE WHEN metadata IS NOT NULL THEN metadata ELSE '{}' END as preferences, 
               created_at, updated_at FROM users;
        
        -- Drop old table and rename new one
        DROP TABLE users;
        ALTER TABLE users_new RENAME TO users;
        
        -- Recreate trigger
        CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
        AFTER UPDATE ON users
        BEGIN
          UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `);
      console.log('[DB] Users table schema migrated successfully');
    }
    
    // Verify colors table has organization_id column
    const colorColumns = db.prepare(`
      SELECT name FROM pragma_table_info('colors')
    `).all().map((col: any) => col.name);
    
    if (!colorColumns.includes('organization_id')) {
      console.log('[DB] Adding organization_id to colors table...');
      db.exec(`
        ALTER TABLE colors ADD COLUMN organization_id INTEGER;
        ALTER TABLE colors ADD COLUMN created_by TEXT;
      `);
    }
    
    // Verify products table has organization_id column
    const productColumns = db.prepare(`
      SELECT name FROM pragma_table_info('products')
    `).all().map((col: any) => col.name);
    
    if (!productColumns.includes('organization_id')) {
      console.log('[DB] Adding organization_id to products table...');
      db.exec(`
        ALTER TABLE products ADD COLUMN organization_id INTEGER;
        ALTER TABLE products ADD COLUMN created_by TEXT;
      `);
    }
    
    console.log('[DB] Table schemas verified');
  } catch (error) {
    console.error('[DB] Error verifying table schemas:', error);
    // Don't throw - allow app to continue
  }
}

/**
 * Create the optimized schema
 * @deprecated This function is not currently used - schema creation is handled inline
 */
async function _createOptimizedSchema(): Promise<void> {
  if (!db) {throw new Error('Database not initialized');}

  const schemaPath = path.join(__dirname, '..', '..', '..', 'chromasync-optimized-schema.md');
  console.log('[DB] Reading schema from:', schemaPath);
  
  // For now, let's create the essential tables inline
  // In production, this would read from the schema file
  
  const tx = db.transaction(() => {
    // Create color sources table
    db.exec(`
      CREATE TABLE IF NOT EXISTS color_sources (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        is_system BOOLEAN NOT NULL DEFAULT FALSE,
        properties JSON
      );
      
      -- Pre-populate with standard sources
      INSERT OR IGNORE INTO color_sources (id, code, name, is_system) VALUES
      (1, 'user', 'User Created', FALSE),
      (2, 'pantone', 'PANTONE®', TRUE),
      (3, 'ral', 'RAL', TRUE),
      (4, 'ncs', 'NCS', TRUE);
    `);

    // Create products table
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL COLLATE NOCASE,
        sku TEXT UNIQUE COLLATE NOCASE,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        metadata JSON,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        
        CHECK (length(trim(name)) > 0),
        CHECK (external_id GLOB '[0-9a-f]*-[0-9a-f]*-[0-9a-f]*-[0-9a-f]*-[0-9a-f]*')
      );
      
      CREATE INDEX IF NOT EXISTS idx_products_active_name 
      ON products(is_active, name) WHERE is_active = TRUE;
    `);

    // Create colors table
    db.exec(`
      CREATE TABLE IF NOT EXISTS colors (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        source_id INTEGER NOT NULL REFERENCES color_sources(id),
        code TEXT NOT NULL COLLATE NOCASE,
        display_name TEXT COLLATE NOCASE,
        hex CHAR(7) NOT NULL,
        is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
        is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
        is_effect BOOLEAN NOT NULL DEFAULT FALSE,
        properties JSON,
        search_terms TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP,
        
        CHECK (
          length(hex) = 7 AND 
          substr(hex, 1, 1) = '#' AND
          hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
        ),
        CHECK (length(trim(code)) > 0),
        UNIQUE (source_id, code)
      );
      
      -- Generated column for active status
      ALTER TABLE colors ADD COLUMN is_active BOOLEAN 
      GENERATED ALWAYS AS (deleted_at IS NULL) STORED;
      
      CREATE INDEX IF NOT EXISTS idx_colors_active_complete 
      ON colors(id, external_id, source_id, code, hex, is_gradient, is_active) 
      WHERE is_active = TRUE;
    `);

    // Create color space tables
    db.exec(`
      CREATE TABLE IF NOT EXISTS color_cmyk (
        color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
        c INTEGER NOT NULL CHECK (c BETWEEN 0 AND 100),
        m INTEGER NOT NULL CHECK (m BETWEEN 0 AND 100),
        y INTEGER NOT NULL CHECK (y BETWEEN 0 AND 100),
        k INTEGER NOT NULL CHECK (k BETWEEN 0 AND 100)
      ) WITHOUT ROWID;
      
      CREATE TABLE IF NOT EXISTS color_rgb (
        color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
        r INTEGER NOT NULL CHECK (r BETWEEN 0 AND 255),
        g INTEGER NOT NULL CHECK (g BETWEEN 0 AND 255),
        b INTEGER NOT NULL CHECK (b BETWEEN 0 AND 255)
      ) WITHOUT ROWID;
      
      CREATE TABLE IF NOT EXISTS color_lab (
        color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
        l REAL NOT NULL CHECK (l BETWEEN 0 AND 100),
        a REAL NOT NULL CHECK (a BETWEEN -128 AND 127),
        b REAL NOT NULL CHECK (b BETWEEN -128 AND 127),
        illuminant TEXT NOT NULL DEFAULT 'D65',
        observer TEXT NOT NULL DEFAULT '2'
      ) WITHOUT ROWID;
      
      CREATE TABLE IF NOT EXISTS color_hsl (
        color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
        h INTEGER NOT NULL CHECK (h BETWEEN 0 AND 360),
        s INTEGER NOT NULL CHECK (s BETWEEN 0 AND 100),
        l INTEGER NOT NULL CHECK (l BETWEEN 0 AND 100)
      ) WITHOUT ROWID;
    `);

    // Create product_colors junction table
    db.exec(`
      CREATE TABLE IF NOT EXISTS product_colors (
        product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
        color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE RESTRICT,
        display_order INTEGER NOT NULL DEFAULT 0,
        usage_type TEXT DEFAULT 'standard',
        quantity REAL,
        metadata JSON,
        added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        
        PRIMARY KEY (product_id, color_id),
        CHECK (usage_type IN ('standard', 'primary', 'accent', 'variant'))
      ) WITHOUT ROWID;
      
      CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
    `);

    // Create gradient_stops table
    db.exec(`
      CREATE TABLE IF NOT EXISTS gradient_stops (
        gradient_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
        stop_index INTEGER NOT NULL,
        position REAL NOT NULL,
        hex CHAR(7) NOT NULL,
        
        PRIMARY KEY (gradient_id, stop_index),
        CHECK (position BETWEEN 0.0 AND 1.0),
        CHECK (
          length(hex) = 7 AND 
          substr(hex, 1, 1) = '#' AND
          hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
        ),
        CHECK (stop_index >= 0)
      ) WITHOUT ROWID;
    `);

    // Create audit_log table
    db.exec(`
      CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY,
        table_name TEXT NOT NULL,
        record_id INTEGER NOT NULL,
        action TEXT NOT NULL,
        changes JSON,
        user_id TEXT,
        occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        
        CHECK (action IN ('INSERT', 'UPDATE', 'DELETE'))
      );
      
      CREATE INDEX IF NOT EXISTS idx_audit_lookup 
      ON audit_log(table_name, record_id, occurred_at);
    `);

    // Create triggers
    db.exec(`
      -- Auto-update timestamp trigger for products
      CREATE TRIGGER IF NOT EXISTS products_update_timestamp 
      AFTER UPDATE ON products
      FOR EACH ROW
      WHEN NEW.updated_at = OLD.updated_at
      BEGIN
        UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
      
      -- Auto-update timestamp trigger for colors
      CREATE TRIGGER IF NOT EXISTS colors_update_timestamp 
      AFTER UPDATE ON colors
      FOR EACH ROW
      WHEN NEW.updated_at = OLD.updated_at
      BEGIN
        UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    // Create views
    db.exec(`
      -- Complete color information view
      CREATE VIEW IF NOT EXISTS v_colors AS
      WITH color_usage AS (
        SELECT 
          color_id,
          COUNT(*) as usage_count,
          GROUP_CONCAT(product_id) as product_ids
        FROM product_colors
        GROUP BY color_id
      )
      SELECT 
        c.id,
        c.external_id,
        cs.code as source,
        cs.is_system as is_library,
        c.code,
        c.display_name,
        c.hex,
        
        -- CMYK values
        cmyk.c as cyan,
        cmyk.m as magenta,
        cmyk.y as yellow,
        cmyk.k as black,
        
        -- RGB values
        rgb.r as red,
        rgb.g as green,
        rgb.b as blue,
        
        -- LAB values
        lab.l as lab_l,
        lab.a as lab_a,
        lab.b as lab_b,
        
        -- HSL values
        hsl.h as hue,
        hsl.s as saturation,
        hsl.l as lightness,
        
        -- Characteristics
        c.is_gradient,
        c.is_metallic,
        c.is_effect,
        c.is_active,
        
        -- Usage
        COALESCE(cu.usage_count, 0) as product_count,
        cu.product_ids,
        
        c.properties,
        c.created_at,
        c.updated_at
      FROM colors c
      INNER JOIN color_sources cs ON c.source_id = cs.id
      LEFT JOIN color_cmyk cmyk ON c.id = cmyk.color_id
      LEFT JOIN color_rgb rgb ON c.id = rgb.color_id
      LEFT JOIN color_lab lab ON c.id = lab.color_id
      LEFT JOIN color_hsl hsl ON c.id = hsl.color_id
      LEFT JOIN color_usage cu ON c.id = cu.color_id
      WHERE c.is_active = TRUE;
    `);

    console.log('[DB] Optimized schema created successfully');
  });

  tx();
}


/**
 * Get or create integer ID for a UUID
 */
function getOrCreateIntegerId(uuid: string, table: 'products' | 'colors'): number {
  // Check cache first
  if (idMappingCache.has(uuid)) {
    return idMappingCache.get(uuid)!;
  }

  // Check database
  const row = db!.prepare(`SELECT id FROM ${table} WHERE external_id = ?`).get(uuid) as {id: number} | undefined;
  if (row) {
    idMappingCache.set(uuid, row.id);
    reverseIdMappingCache.set(row.id, uuid);
    return row.id;
  }

  // ID doesn't exist yet, will be created during insert
  return 0;
}

/**
 * Close the database connection
 */
export function closeDatabase(): void {
  if (db) {
    // Run optimize before closing
    db.exec('PRAGMA optimize');
    db.close();
    db = null;
  }
}

// Export compatibility functions that match the old API
export function getDatabase(): any | null {
  return db;
}

// Continue in next part...
// ============================================
// Compatibility Layer - Product Operations
// ============================================

/**
 * Get all products (maintains old API)
 */
export function getAllProducts(): any[] {
  if (!db) {throw new Error('Database not initialized');}

  const products = db.prepare(`
    SELECT 
      p.external_id as id,
      p.name,
      p.metadata->>'description' as description,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      p.metadata->>'createdBy' as createdBy,
      p.metadata->>'updatedBy' as updatedBy
    FROM products p
    WHERE p.is_active = TRUE
    ORDER BY p.name
  `).all();

  return products;
}

/**
 * Get product by ID (maintains old API)
 */
export function getProductById(id: string): any | null {
  if (!db) {throw new Error('Database not initialized');}

  const product = db.prepare(`
    SELECT 
      p.external_id as id,
      p.name,
      p.metadata->>'description' as description,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      p.metadata->>'createdBy' as createdBy,
      p.metadata->>'updatedBy' as updatedBy
    FROM products p
    WHERE p.external_id = ? AND p.is_active = TRUE
  `).get(id) as any;

  return product || null;
}

/**
 * Create product (maintains old API)
 */
export function createProduct(data: { name: string; description?: string; createdBy?: string }): any {
  if (!db) {throw new Error('Database not initialized');}

  const id = uuidv4();
  const metadata = {
    description: data.description,
    createdBy: data.createdBy
  };

  db.prepare(`
    INSERT INTO products (external_id, name, metadata)
    VALUES (?, ?, json(?))
  `).run(id, data.name, JSON.stringify(metadata));

  return {
    id,
    name: data.name,
    description: data.description,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: data.createdBy
  };
}

/**
 * Update product (maintains old API)
 */
export function updateProduct(id: string, data: { name?: string; description?: string; updatedBy?: string }): any | null {
  if (!db) {throw new Error('Database not initialized');}

  const existing = getProductById(id);
  if (!existing) {return null;}

  const metadata = {
    description: data.description || existing.description,
    createdBy: existing.createdBy,
    updatedBy: data.updatedBy
  };

  db.prepare(`
    UPDATE products 
    SET name = COALESCE(?, name),
        metadata = json(?),
        updated_at = CURRENT_TIMESTAMP
    WHERE external_id = ?
  `).run(data.name, JSON.stringify(metadata), id);

  return getProductById(id);
}

/**
 * Delete product (maintains old API)
 */
export function deleteProduct(id: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  const result = db.prepare(`
    UPDATE products 
    SET is_active = FALSE,
        updated_at = CURRENT_TIMESTAMP
    WHERE external_id = ?
  `).run(id);

  return result.changes > 0;
}

// ============================================
// Compatibility Layer - Color Operations
// ============================================

/**
 * Get all colors (maintains old API)
 */
export function getAllColors(): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}

  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k,
      GROUP_CONCAT(pc.product_id) as product_ids
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    LEFT JOIN product_colors pc ON c.id = pc.color_id
    WHERE c.is_active = TRUE
    GROUP BY c.id
    ORDER BY c.code
  `).all() as any[];

  // Convert to old format
  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    // Get product name if associated
    let productName = '';
    if (color.product_ids) {
      const productId = reverseIdMappingCache.get(parseInt(color.product_ids.split(',')[0]));
      if (productId) {
        const product = getProductById(productId);
        if (product) {productName = product.name;}
      }
    }

    return {
      id: color.external_id,
      product: productName,
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Get color by ID (maintains old API)
 */
export function getColorById(id: string): ColorEntry | null {
  if (!db) {throw new Error('Database not initialized');}

  const color = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.external_id = ? AND c.is_active = TRUE
  `).get(id) as any;

  if (!color) {return null;}

  // Get associated product
  const productAssoc = db.prepare(`
    SELECT p.external_id, p.name
    FROM product_colors pc
    JOIN products p ON pc.product_id = p.id
    WHERE pc.color_id = ?
    LIMIT 1
  `).get(color.internal_id) as any;

  const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';

  return {
    id: color.external_id,
    product: productAssoc?.name || '',
    name: color.display_name || color.code,
    code: color.code,
    hex: color.hex,
    cmyk: cmyk,
    notes: color.properties?.notes,
    gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
    isLibrary: color.source_id !== 1,
    createdAt: color.created_at,
    updatedAt: color.updated_at
  } as ColorEntry;
}

/**
 * Get gradient info for a color
 */
function getGradientInfo(colorId: number): any {
  const stops = db!.prepare(`
    SELECT stop_index, position, hex
    FROM gradient_stops
    WHERE gradient_id = ?
    ORDER BY stop_index
  `).all(colorId) as Array<{stop_index: number, position: number, hex: string}>;

  if (stops.length === 0) {return undefined;}

  const gradientStops = stops.map(stop => ({
    color: stop.hex,
    position: stop.position,
    cmyk: undefined // Can be calculated if needed
  }));

  const colorStops = gradientStops
    .map(stop => `${stop.color} ${stop.position * 100}%`)
    .join(', ');

  return {
    gradientStops,
    gradientCSS: `linear-gradient(90deg, ${colorStops})`
  };
}

/**
 * Create color (maintains old API)
 */
export function createColor(data: Partial<ColorEntry> & { createdBy?: string }): ColorEntry {
  if (!db) {throw new Error('Database not initialized');}

  const tx = db.transaction(() => {
    const externalId = data.id || uuidv4();
    const sourceId = data.isLibrary ? 2 : 1; // 2 for PANTONE, 1 for user
    
    // Validate and standardize hex
    const hex = isValidHex(data.hex!) ? data.hex!.toUpperCase() : '#000000';
    
    // Parse CMYK
    let cmykValues = [0, 0, 0, 0];
    if (data.cmyk) {
      const parsed = standardizeCMYK(data.cmyk);
      if (parsed && typeof parsed === 'string') {
        cmykValues = parsed.split(',').map(v => parseInt(v));
      }
    } else if (hex !== '#000000') {
      // Calculate CMYK from hex
      const calculated = hexToCmyk(hex);
      if (calculated) {
        cmykValues = [calculated.c, calculated.m, calculated.y, calculated.k];
      }
    }

    // Insert color
    const result = db.prepare(`
      INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, properties)
      VALUES (?, ?, ?, ?, ?, ?, json(?))
    `).run(
      externalId,
      sourceId,
      data.code || 'CUSTOM',
      data.name || data.code || 'Custom Color',
      hex,
      data.gradient ? 1 : 0,
      JSON.stringify({ notes: data.notes, createdBy: data.createdBy })
    );

    const colorId = result.lastInsertRowid;

    // Insert CMYK values
    db.prepare(`
      INSERT INTO color_cmyk (color_id, c, m, y, k)
      VALUES (?, ?, ?, ?, ?)
    `).run(colorId, cmykValues[0], cmykValues[1], cmykValues[2], cmykValues[3]);

    // Insert RGB values (calculated from hex)
    const rgb = hexToRgb(hex);
    if (rgb) {
      db.prepare(`
        INSERT INTO color_rgb (color_id, r, g, b)
        VALUES (?, ?, ?, ?)
      `).run(colorId, rgb.r, rgb.g, rgb.b);
    }

    // Handle gradient stops if present
    if (data.gradient && data.gradient.gradientStops) {
      data.gradient.gradientStops.forEach((stop, index) => {
        db.prepare(`
          INSERT INTO gradient_stops (gradient_id, stop_index, position, hex)
          VALUES (?, ?, ?, ?)
        `).run(colorId, index, stop.position, stop.color);
      });
    }

    // Associate with product if provided
    if (data.product) {
      const product = db.prepare('SELECT id FROM products WHERE name = ?').get(data.product) as {id: number} | undefined;
      if (product) {
        db.prepare(`
          INSERT INTO product_colors (product_id, color_id)
          VALUES (?, ?)
        `).run(product.id, colorId);
      }
    }

    // Update caches
    idMappingCache.set(externalId, colorId as number);
    reverseIdMappingCache.set(colorId as number, externalId);

    return {
      id: externalId,
      product: data.product || '',
      name: data.name || data.code || 'Custom Color',
      code: data.code || 'CUSTOM',
      hex: hex,
      cmyk: cmykValues.join(','),
      notes: data.notes,
      gradient: data.gradient,
      isLibrary: data.isLibrary || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as ColorEntry;
  });

  return tx();
}
/**
 * Update color (maintains old API)
 */
export function updateColor(id: string, data: Partial<ColorEntry> & { updatedBy?: string }): ColorEntry | null {
  if (!db) {throw new Error('Database not initialized');}

  const existing = getColorById(id);
  if (!existing) {return null;}

  const tx = db.transaction(() => {
    // Get internal ID
    const internalId = getOrCreateIntegerId(id, 'colors');
    
    // Update main color record
    const updates: string[] = [];
    const params: any[] = [];

    if (data.code !== undefined) {
      updates.push('code = ?');
      params.push(data.code);
    }
    if (data.name !== undefined) {
      updates.push('display_name = ?');
      params.push(data.name);
    }
    if (data.hex !== undefined && isValidHex(data.hex)) {
      updates.push('hex = ?');
      params.push(data.hex.toUpperCase());
    }
    if (data.notes !== undefined) {
      const properties = existing.notes ? JSON.parse(existing.notes) : {};
      properties.notes = data.notes;
      properties.updatedBy = data.updatedBy;
      updates.push('properties = json(?)');
      params.push(JSON.stringify(properties));
    }

    if (updates.length > 0) {
      params.push(id);
      db.prepare(`
        UPDATE colors 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE external_id = ?
      `).run(...params);
    }

    // Update CMYK if provided
    if (data.cmyk) {
      const cmykValues = standardizeCMYK(data.cmyk)?.split(',').map(v => parseInt(v));
      if (cmykValues && cmykValues.length === 4) {
        db.prepare(`
          INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(internalId, ...cmykValues);
      }
    }

    // Update RGB if hex changed
    if (data.hex && isValidHex(data.hex)) {
      const rgb = hexToRgb(data.hex);
      if (rgb) {
        db.prepare(`
          INSERT OR REPLACE INTO color_rgb (color_id, r, g, b)
          VALUES (?, ?, ?, ?)
        `).run(internalId, rgb.r, rgb.g, rgb.b);
      }
    }

    return getColorById(id);
  });

  return tx();
}

/**
 * Delete color (maintains old API)
 */
export function deleteColor(id: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  const result = db.prepare(`
    UPDATE colors 
    SET deleted_at = CURRENT_TIMESTAMP
    WHERE external_id = ? AND is_active = TRUE
  `).run(id);

  return result.changes > 0;
}

/**
 * Get colors by product (maintains old API)
 */
export function getColorsByProduct(productId: string): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}

  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k,
      p.name as product_name
    FROM colors c
    JOIN product_colors pc ON c.id = pc.color_id
    JOIN products p ON pc.product_id = p.id
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE p.external_id = ? AND c.is_active = TRUE
    ORDER BY pc.display_order, c.code
  `).all(productId) as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: color.product_name,
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Associate color with product
 */
export function associateColorWithProduct(colorId: string, productId: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  try {
    const colorInternalId = getOrCreateIntegerId(colorId, 'colors');
    const productInternalId = getOrCreateIntegerId(productId, 'products');

    if (!colorInternalId || !productInternalId) {return false;}

    db.prepare(`
      INSERT OR IGNORE INTO product_colors (product_id, color_id)
      VALUES (?, ?)
    `).run(productInternalId, colorInternalId);

    return true;
  } catch (error) {
    console.error('[DB] Failed to associate color with product:', error);
    return false;
  }
}

/**
 * Disassociate color from product
 */
export function disassociateColorFromProduct(colorId: string, productId: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  try {
    const colorInternalId = getOrCreateIntegerId(colorId, 'colors');
    const productInternalId = getOrCreateIntegerId(productId, 'products');

    if (!colorInternalId || !productInternalId) {return false;}

    const result = db.prepare(`
      DELETE FROM product_colors 
      WHERE product_id = ? AND color_id = ?
    `).run(productInternalId, colorInternalId);

    return result.changes > 0;
  } catch (error) {
    console.error('[DB] Failed to disassociate color from product:', error);
    return false;
  }
}

// ============================================
// Performance Monitoring & Maintenance
// ============================================

/**
 * Run database maintenance
 */
export function runMaintenance(): void {
  if (!db) {throw new Error('Database not initialized');}

  console.log('[DB] Running maintenance...');
  
  // Checkpoint WAL
  db.pragma('wal_checkpoint(TRUNCATE)');
  
  // Analyze tables for query optimizer
  db.exec('ANALYZE');
  
  // Run optimize
  db.pragma('optimize');
  
  console.log('[DB] Maintenance complete');
}

/**
 * Get database statistics
 */
export function getDatabaseStats(): any {
  if (!db) {throw new Error('Database not initialized');}

  const stats = {
    products: db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = TRUE').get(),
    colors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_active = TRUE').get(),
    libraryColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id != 1 AND is_active = TRUE').get(),
    userColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id = 1 AND is_active = TRUE').get(),
    gradients: db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_gradient = TRUE AND is_active = TRUE').get(),
    associations: db.prepare('SELECT COUNT(*) as count FROM product_colors').get(),
    walSize: 0
  };

  // Check WAL file size
  try {
    const walPath = getDatabasePath() + '-wal';
    if (fs.existsSync(walPath)) {
      stats.walSize = fs.statSync(walPath).size;
    }
  } catch (_error) {
    // Ignore
  }

  return stats;
}

// ============================================
// Search Operations
// ============================================

/**
 * Search colors by name or code
 */
export function searchColors(query: string): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}
  
  const searchTerm = `%${query}%`;
  
  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.is_active = TRUE
      AND (c.code LIKE ? OR c.display_name LIKE ? OR c.search_terms LIKE ?)
    ORDER BY 
      CASE 
        WHEN c.code = ? THEN 1
        WHEN c.code LIKE ? THEN 2
        ELSE 3
      END,
      c.code
    LIMIT 100
  `).all(searchTerm, searchTerm, searchTerm, query, `${query}%`) as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: '',
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Find similar colors by hex value
 */
export function findSimilarColors(_hex: string, _threshold: number = 50): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}
  
  // This would use the color_distance custom function in production
  // For now, we'll do a simple hex comparison
  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.is_active = TRUE AND c.is_gradient = FALSE
    ORDER BY c.hex
    LIMIT 20
  `).all() as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: '',
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

// Export all functions
export default {
  initDatabase,
  closeDatabase,
  getDatabase,
  
  // Products
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  
  // Colors
  getAllColors,
  getColorById,
  createColor,
  updateColor,
  deleteColor,
  getColorsByProduct,
  searchColors,
  findSimilarColors,
  
  // Associations
  associateColorWithProduct,
  disassociateColorFromProduct,
  
  // Maintenance
  runMaintenance,
  getDatabaseStats
};