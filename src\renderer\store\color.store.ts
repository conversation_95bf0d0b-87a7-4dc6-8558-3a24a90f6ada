/**
 * @file color.store.ts
 * @description Zustand store for color state management
 */

import { create } from 'zustand';
import { ColorEntry, NewColorEntry, UpdateColorEntry } from '../../shared/types/color.types';
// Removed sampleColors import - use loadSampleData function instead
import { standardizeCMYK } from '../../shared/utils/color';
import { loadSampleData } from '../utils/sampleData';
import { loadPantoneLibrary } from '../utils/pantoneColors';
import { loadRalLibrary } from '../utils/ralColors';
import { persist } from 'zustand/middleware';
import { measureAsync, logError, logInfo } from '../utils/errorLogger';
import { useOrganizationStore } from './organization.store';

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Utility function to safely extract error messages
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {return error.message;}
  return String(error);
};

// Get initial dark mode preference from localStorage or system preference
const getInitialDarkMode = () => {
  if (typeof window === 'undefined') {return false;}
  
  const savedMode = localStorage.getItem('darkMode');
  if (savedMode) {
    return savedMode === 'dark';
  }
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
};

// Initialize dark mode on the document
const initialDarkMode = getInitialDarkMode();
if (typeof document !== 'undefined') {
  // Remove any existing theme class and add the correct one
  document.documentElement.classList.remove('dark', 'light');
  document.documentElement.classList.add(initialDarkMode ? 'dark' : 'light');
  
  // Also apply to body
  document.body.classList.remove('dark', 'light');
  document.body.classList.add(initialDarkMode ? 'dark' : 'light');
  
  // Apply transition for smooth theme changes
  document.body.style.transition = 'background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease';
  
  console.log('Initial dark mode setup:', initialDarkMode ? 'dark' : 'light');
}

export interface ColorState {
  // State
  colors: ColorEntry[];
  selectedColor: ColorEntry | null;
  isLoading: boolean;
  error: string | null;
  viewMode: 'table' | 'swatches' | 'codes' | 'products';
  searchQuery: string;
  darkMode: boolean;
  pantoneColors: ColorEntry[];
  ralColors: ColorEntry[];
  
  // Actions
  fetchColors: () => Promise<void>;
  loadColors: () => Promise<void>; // Alias for fetchColors for backward compatibility
  getColorById: (id: string) => Promise<ColorEntry | null>;
  addColor: (color: NewColorEntry) => Promise<ColorEntry | null>;
  updateColor: (id: string, updates: UpdateColorEntry) => Promise<ColorEntry | null>;
  deleteColor: (id: string) => Promise<boolean>;
  clearColors: () => Promise<boolean>;
  importColors: (mergeMode?: 'replace' | 'merge', customFilePath?: string, format?: 'json' | 'csv') => Promise<{ success: boolean; message: string }>;
  exportColors: (customFilePath?: string, format?: 'json' | 'csv') => Promise<{ success: boolean; message: string }>;
  
  // UI state
  setSelectedColor: (color: ColorEntry | null) => void;
  setViewMode: (mode: 'table' | 'swatches' | 'codes' | 'products') => void;
  setSearchQuery: (query: string) => void;
  toggleDarkMode: () => void;
  
  // Helper methods to access color libraries
  getPantoneColors: () => ColorEntry[];
  getRalColors: () => ColorEntry[];
  
  // Get all colors including user colors, Pantone, and RAL
  getAllColors: () => ColorEntry[];
}

export const useColorStore = create<ColorState>()(
  persist(
    (set, get) => ({
      // Initial state with safe defaults
      colors: [],
      selectedColor: null,
      isLoading: false,
      error: null,
      viewMode: 'table',
      searchQuery: '',
      darkMode: getInitialDarkMode(),
      pantoneColors: [],
      ralColors: [],
      
      // Actions
      fetchColors: async () => {
        // Check if organization is selected before fetching
        const orgStore = useOrganizationStore.getState();
        if (!orgStore.currentOrganization) {
          console.log('[ColorStore] No organization selected, skipping color fetch');
          set({ colors: [], isLoading: false });
          return;
        }
        
        return measureAsync('color_store_fetch_colors', async () => {
          set({ isLoading: true, error: null });
          try {
            let colors: ColorEntry[] = [];
            
            // Always try to load from API first when organization is selected
            // Only fall back to sample data if API fails or no organization
            try {
              const result = await window.colorAPI.getAll();

              // Handle new enterprise response format
              if (result && typeof result === 'object' && 'success' in result && result.success && Array.isArray(result.data)) {
                colors = result.data;
                const pantoneColors = loadPantoneLibrary();
                const ralColors = loadRalLibrary();
                set({ colors, pantoneColors, ralColors, isLoading: false });
                logInfo('Colors loaded from API', {
                  colorCount: colors.length,
                  pantoneCount: pantoneColors?.length || 0,
                  ralCount: ralColors?.length || 0
                });
                return;
              }

              // Handle legacy direct array response (backward compatibility)
              if (result && Array.isArray(result) && result.length > 0) {
                colors = result;
                const pantoneColors = loadPantoneLibrary();
                const ralColors = loadRalLibrary();
                set({ colors, pantoneColors, ralColors, isLoading: false });
                logInfo('Colors loaded from API (legacy format)', {
                  colorCount: colors.length,
                  pantoneCount: pantoneColors?.length || 0,
                  ralCount: ralColors?.length || 0
                });
                return;
              }
            } catch (apiError) {
              console.log('[ColorStore] API call failed, falling back to sample data:', apiError);
            }
            
            // Fallback to sample data in development or if API fails
            if (process.env.NODE_ENV === 'development') {
              // Use current organization ID for sample data if available
              const currentOrgId = orgStore.currentOrganization?.external_id;
              colors = loadSampleData(currentOrgId);
              const pantoneColors = loadPantoneLibrary();
              const ralColors = loadRalLibrary();
              set({ colors, pantoneColors, ralColors, isLoading: false });
              logInfo('Sample colors loaded as fallback', { 
                colorCount: colors.length,
                pantoneCount: pantoneColors?.length || 0,
                ralCount: ralColors?.length || 0,
                organizationId: currentOrgId
              });
              return;
            }
            
            // In production, load from backend
            const result = await window.colorAPI.getAll();

            // Handle new enterprise response format
            if (result && typeof result === 'object' && 'success' in result && result.success && Array.isArray(result.data)) {
              colors = result.data;
              // Also load the Pantone and RAL libraries in production
              const pantoneColors = loadPantoneLibrary() || [];
              const ralColors = loadRalLibrary() || [];
              set({ colors, pantoneColors, ralColors, isLoading: false });
              logInfo('Colors loaded from backend', {
                colorCount: colors.length,
                pantoneCount: pantoneColors.length,
                ralCount: ralColors.length
              });
            } else if (result && Array.isArray(result)) {
              // Handle legacy direct array response (backward compatibility)
              colors = result;
              const pantoneColors = loadPantoneLibrary() || [];
              const ralColors = loadRalLibrary() || [];
              set({ colors, pantoneColors, ralColors, isLoading: false });
              logInfo('Colors loaded from backend (legacy format)', {
                colorCount: colors.length,
                pantoneCount: pantoneColors.length,
                ralCount: ralColors.length
              });
            } else {
              // Still load libraries even if no user colors
              const pantoneColors = loadPantoneLibrary() || [];
              const ralColors = loadRalLibrary() || [];
              set({ colors: [], pantoneColors, ralColors, error: 'Failed to fetch colors', isLoading: false });
              logError('Failed to fetch colors from backend', { result });
            }
          } catch (error) {
            const errorMessage = (error as Error).message;
            set({ error: errorMessage, isLoading: false });
            logError('Error fetching colors', { error: errorMessage, stack: (error as Error).stack });
          }
        }, { source: 'color_store' });
      },
      
      // Alias for fetchColors for backward compatibility
      loadColors: async () => {
        return get().fetchColors();
      },
      
      getColorById: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          // In development mode, find in sample data
          if (isDevelopment) {
            const orgStore = useOrganizationStore.getState();
            const currentOrgId = orgStore.currentOrganization?.external_id;
            const sampleColorsWithOrg = loadSampleData(currentOrgId);
            const color = sampleColorsWithOrg.find(c => c.id === id) || null;
            set({ isLoading: false });
            return color;
          }
          
          // In production, fetch from API
          const result = await window.colorAPI.getById(id);
          set({ isLoading: false });

          // Handle enterprise response format
          if (result && typeof result === 'object' && 'success' in result && result.success && result.data) {
            return result.data;
          }

          return null;
        } catch (error: unknown) {
          console.error(`Error fetching color ${id}:`, error);
          set({ error: getErrorMessage(error) || `Failed to fetch color ${id}`, isLoading: false });
          return null;
        }
      },
      
      addColor: async (color: NewColorEntry) => {
        set({ isLoading: true, error: null });
        try {
          // Normalize CMYK format
          const normalizedColor = {
            ...color,
            cmyk: standardizeCMYK(color.cmyk)
          };

          // Even in development, we need to use the IPC channel to persist
          // the color in the main process's database for proper collections integration
          if (isDevelopment) {
            let persistedColor: ColorEntry;
            try {
              // Send to main process first
              const ipcResult = await window.colorAPI.add(normalizedColor);
              persistedColor = ipcResult as ColorEntry;
              console.log('Color added via IPC in development mode:', persistedColor);
            } catch (devError: unknown) {
              // Fallback for development testing if IPC fails
              console.warn('IPC color addition failed in dev mode, falling back to local:', getErrorMessage(devError));
              const now = new Date().toISOString();
              persistedColor = {
                ...normalizedColor,
                id: `dev-${Date.now()}`,
                createdAt: now,
                updatedAt: now
              };
            }
            
            // Update local state
            set((state) => ({ 
              colors: [persistedColor, ...state.colors],
              isLoading: false 
            }));
            
            return persistedColor;
          }
          
          // In production, use API
          const newColor = await window.colorAPI.add(normalizedColor);
          set((state) => ({ 
            colors: [newColor, ...state.colors],
            isLoading: false 
          }));
          return newColor;
        } catch (error: unknown) {
          console.error('Error adding color:', error);
          set({ error: getErrorMessage(error) || 'Failed to add color', isLoading: false });
          return null;
        }
      },
      
      updateColor: async (id: string, updates: UpdateColorEntry) => {
        set({ isLoading: true, error: null });
        try {
          // Normalize CMYK format if it's being updated
          const normalizedUpdates = {
            ...updates,
            ...(updates.cmyk ? { cmyk: standardizeCMYK(updates.cmyk) } : {})
          };

          // In development, update locally
          if (isDevelopment) {
            const now = new Date().toISOString();
            const colorToUpdate = get().colors.find(c => c.id === id);
            
            if (!colorToUpdate) {
              throw new Error(`Color with id ${id} not found`);
            }
            
            const updatedColor: ColorEntry = {
              ...colorToUpdate,
              ...normalizedUpdates,
              updatedAt: now
            };
            
            set((state) => ({
              colors: state.colors.map(color => 
                color.id === id ? updatedColor : color
              ),
              selectedColor: state.selectedColor?.id === id ? updatedColor : state.selectedColor,
              isLoading: false
            }));
            
            return updatedColor;
          }
          
          // In production, use API
          const updatedColor = await window.colorAPI.update(id, normalizedUpdates);
          set((state) => ({
            colors: state.colors.map(color => 
              color.id === id ? updatedColor : color
            ),
            selectedColor: state.selectedColor?.id === id ? updatedColor : state.selectedColor,
            isLoading: false
          }));
          return updatedColor;
        } catch (error: unknown) {
          console.error(`Error updating color ${id}:`, error);
          set({ error: getErrorMessage(error) || `Failed to update color ${id}`, isLoading: false });
          return null;
        }
      },
      
      deleteColor: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          // In development, delete locally
          if (isDevelopment) {
            set((state) => ({
              colors: state.colors.filter(color => color.id !== id),
              selectedColor: state.selectedColor?.id === id ? null : state.selectedColor,
              isLoading: false
            }));
            return true;
          }
          
          // In production, use API
          const success = await window.colorAPI.delete(id);
          if (success) {
            set((state) => ({
              colors: state.colors.filter(color => color.id !== id),
              selectedColor: state.selectedColor?.id === id ? null : state.selectedColor,
              isLoading: false
            }));
          }
          return success;
        } catch (error: unknown) {
          console.error(`Error deleting color ${id}:`, error);
          set({ error: getErrorMessage(error) || `Failed to delete color ${id}`, isLoading: false });
          return false;
        }
      },
      
      clearColors: async () => {
        set({ isLoading: true, error: null });
        try {
          // In development, just clear the array
          if (isDevelopment) {
            set({ colors: [], selectedColor: null, isLoading: false });
            return true;
          }
          
          // In production, use API
          const success = await window.colorAPI.clearAll();
          if (success) {
            set({ colors: [], selectedColor: null, isLoading: false });
          }
          return success;
        } catch (error: unknown) {
          console.error('Error clearing colors:', error);
          set({ error: getErrorMessage(error) || 'Failed to clear colors', isLoading: false });
          return false;
        }
      },
      
      importColors: async (mergeMode = 'replace', customFilePath?: string, format: 'json' | 'csv' = 'json') => {
        set({ isLoading: true, error: null });
        try {
          console.log(`Store: Starting import process (mode: ${mergeMode}, format: ${format})`);
          
          // Safety check for window.colorAPI
          if (!window.colorAPI) {
            console.error('Store: window.colorAPI is not available');
            throw new Error('Import API not available');
          }
          
          // Safety check for import function
          if (typeof window.colorAPI.importColors !== 'function') {
            console.error('Store: window.colorAPI.importColors is not a function');
            throw new Error('Import function not available');
          }
          
          console.log('Store: Calling window.colorAPI.importColors');
          const result = await window.colorAPI.importColors(mergeMode, customFilePath, format);
          console.log('Store: Import result from API:', result);
          
          set({ isLoading: false });
          
          // Process the result
          if (result && result.success) {
            // In development, use sample data
            if (isDevelopment) {
              const orgStore = useOrganizationStore.getState();
              const currentOrgId = orgStore.currentOrganization?.external_id;
              const sampleColorsWithOrg = loadSampleData(currentOrgId);
              set({ colors: sampleColorsWithOrg });
            } else {
              // In production, refresh colors
              await get().fetchColors();
            }
            
            return { 
              success: true, 
              message: result.message || 'Import successful' 
            };
          } else {
            return { 
              success: false, 
              message: result?.message || 'Import failed or was canceled' 
            };
          }
        } catch (error: unknown) {
          console.error('Store: Error importing colors:', error);
          set({ error: getErrorMessage(error) || 'Failed to import colors', isLoading: false });
          return { success: false, message: getErrorMessage(error) || 'Failed to import colors' };
        }
      },
      
      exportColors: async (customFilePath?: string, format?: 'json' | 'csv') => {
        set({ isLoading: true, error: null });
        try {
          console.log(`Store: Starting export process in ${format || 'json'} format`);
          
          // Safety check for window.colorAPI
          if (!window.colorAPI) {
            console.error('Store: window.colorAPI is not available');
            throw new Error('Export API not available');
          }
          
          // Safety check for export function
          if (typeof window.colorAPI.exportColors !== 'function') {
            console.error('Store: window.colorAPI.exportColors is not a function');
            throw new Error('Export function not available');
          }
          
          console.log('Store: Calling window.colorAPI.exportColors');
          const result = await window.colorAPI.exportColors(customFilePath, format);
          console.log('Store: Export result from API:', result);
        
          set({ isLoading: false });
          
          // Process the result
          if (result && result.success) {
            return { 
              success: true, 
              message: result.message || `Export successful as ${format?.toUpperCase() || 'JSON'}` 
            };
          } else {
            return { 
              success: false, 
              message: result?.message || 'Export failed or was canceled' 
            };
          }
        } catch (error: unknown) {
          console.error('Store: Error exporting colors:', error);
          set({ error: getErrorMessage(error) || 'Failed to export colors', isLoading: false });
          return { success: false, message: getErrorMessage(error) || 'Failed to export colors' };
        }
      },
      
      // UI state actions
      setSelectedColor: (color: ColorEntry | null) => {
        set({ selectedColor: color });
      },
      
      setViewMode: (mode: 'table' | 'swatches' | 'codes' | 'products') => {
        set({ viewMode: mode });
      },
      
      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },
      
      toggleDarkMode: () => {
        set((state) => {
          const newDarkMode = !state.darkMode;
          console.log('Toggling dark mode:', state.darkMode, '->', newDarkMode);
          
          // Ensure document is accessed only in browser environment
          if (typeof window !== 'undefined' && typeof document !== 'undefined') {
            // First remove any existing classes to avoid conflicts
            document.documentElement.classList.remove('dark', 'light');
            
            // Add the appropriate class
            document.documentElement.classList.add(newDarkMode ? 'dark' : 'light');
            
            // Force a repaint to ensure all styles are applied
            document.body.style.transition = 'background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease';
            
            // Force body to also adopt dark mode colors
            document.body.classList.remove('dark', 'light');
            document.body.classList.add(newDarkMode ? 'dark' : 'light');
            
            // Apply class to app-container
            const appContainer = document.querySelector('.app-container');
            if (appContainer) {
              appContainer.classList.remove('dark', 'light');
              appContainer.classList.add(newDarkMode ? 'dark' : 'light');
            }
            
            console.log('Dark mode class applied:', newDarkMode ? 'dark' : 'light');
          }
          
          // Save preference to localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('darkMode', newDarkMode ? 'dark' : 'light');
            console.log('Dark mode preference saved to localStorage:', newDarkMode ? 'dark' : 'light');
          }
          
          return { darkMode: newDarkMode };
        });
      },
      
      // Helper methods to access color libraries
      getPantoneColors: () => {
        return get().pantoneColors || [];
      },
      
      getRalColors: () => {
        return get().ralColors || [];
      },
      
      // Get all colors including user colors, Pantone, and RAL
      getAllColors: () => {
        const state = get();
        return [
          ...(state.colors || []), 
          ...(state.pantoneColors || []), 
          ...(state.ralColors || [])
        ];
      },
    }),
    {
      name: 'color-storage',
      // Only persist user-created colors, not the libraries
      partialize: (state) => ({
        colors: state.colors,
        darkMode: state.darkMode,
        viewMode: state.viewMode,
        searchQuery: state.searchQuery
      }),
    }
  )
);
