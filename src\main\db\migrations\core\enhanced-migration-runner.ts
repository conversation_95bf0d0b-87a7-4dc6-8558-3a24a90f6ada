/**
 * Enhanced Migration Runner that uses SafeMigrationRunner
 */

import fs from 'fs';
import path from 'path';
import { SafeMigrationRunner } from './safe-migration-runner';
import { MigrationHelpers } from './migration-helpers';

export interface MigrationFile {
  version: number;
  name: string;
  filename: string;
  sql: string;
}

export class EnhancedMigrationRunner {
  private safeMigrationRunner: SafeMigrationRunner;
  private helpers: MigrationHelpers;
  private db: any;
  
  constructor(database: any) {
    this.db = database;
    this.safeMigrationRunner = new SafeMigrationRunner(database);
    this.helpers = new MigrationHelpers(database);
  }
  
  /**
   * Load migration files from directory
   */
  private loadMigrationFiles(): MigrationFile[] {
    const possiblePaths = [
      path.join(__dirname, '..'),
      path.join(process.cwd(), 'src/main/db/migrations'),
      path.join(process.cwd(), 'out/main/db/migrations')
    ];
    
    let migrationsDir: string | null = null;
    for (const p of possiblePaths) {
      if (fs.existsSync(p)) {
        migrationsDir = p;
        break;
      }
    }
    
    if (!migrationsDir) {
      console.log('[Migrations] No migrations directory found');
      return [];
    }
    
    console.log('[Migrations] Using migrations directory:', migrationsDir);
    
    const files = fs.readdirSync(migrationsDir)
      .filter(f => f.endsWith('.sql') && !f.includes('_safe.sql')) // Skip duplicate safe versions
      .sort();
    
    const migrations: MigrationFile[] = [];
    
    for (const file of files) {
      const match = file.match(/^(\d+)_(.+)\.sql$/);
      if (!match) continue;
      
      const version = parseInt(match[1], 10);
      const name = match[2];
      
      // Check if a safe version exists
      const safeFile = file.replace('.sql', '_safe.sql');
      const sqlFile = fs.existsSync(path.join(migrationsDir, safeFile)) ? safeFile : file;
      
      const sql = fs.readFileSync(path.join(migrationsDir, sqlFile), 'utf8');
      
      migrations.push({ version, name, filename: sqlFile, sql });
    }
    
    return migrations.sort((a, b) => a.version - b.version);
  }
  
  /**
   * Process Migration 9 with proper column checks
   */
  private processMigration9(): boolean {
    console.log('[Migration 9] Processing with safe column checks...');
    
    try {
      // Add device_id columns
      this.helpers.addColumnSafe('products', 'device_id', 'TEXT');
      this.helpers.addColumnSafe('colors', 'device_id', 'TEXT');
      
      // Add conflict resolution columns
      this.helpers.addColumnSafe('products', 'conflict_resolved_at', 'TEXT');
      this.helpers.addColumnSafe('colors', 'conflict_resolved_at', 'TEXT');
      
      // Create sync health monitoring table
      this.helpers.createTableSafe('sync_health', `(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sync_type TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        organization_id INTEGER,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        items_synced_products INTEGER DEFAULT 0,
        items_synced_colors INTEGER DEFAULT 0,
        items_synced_relationships INTEGER DEFAULT 0,
        retry_count INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (organization_id) REFERENCES organizations(id)
      )`);
      
      // Create sync conflicts table
      this.helpers.createTableSafe('sync_conflicts', `(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        external_id TEXT NOT NULL,
        local_data TEXT,
        remote_data TEXT,
        conflict_type TEXT,
        resolved BOOLEAN DEFAULT FALSE,
        resolution_type TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        resolved_at TEXT
      )`);
      
      // Add indexes
      this.helpers.createIndexSafe('idx_sync_health_org', 'sync_health', 'organization_id, created_at DESC');
      this.helpers.createIndexSafe('idx_products_device', 'products', 'device_id');
      this.helpers.createIndexSafe('idx_colors_device', 'colors', 'device_id');
      this.helpers.createIndexSafe('idx_sync_conflicts_unresolved', 'sync_conflicts', 'resolved, created_at');
      
      // Update existing data
      this.db.prepare('UPDATE products SET device_id = ? WHERE device_id IS NULL').run('legacy');
      this.db.prepare('UPDATE colors SET device_id = ? WHERE device_id IS NULL').run('legacy');
      
      return true;
    } catch (error) {
      console.error('[Migration 9] Failed:', error);
      return false;
    }
  }
  
  /**
   * Run all migrations with enhanced safety
   */
  async runMigrations(): Promise<void> {
    console.log('[EnhancedMigrations] Starting migration process...');
    
    const migrations = this.loadMigrationFiles();
    const status = this.safeMigrationRunner.getMigrationStatus();
    const appliedVersions = new Set(status.map((m: any) => m.id));
    
    let successCount = 0;
    let failCount = 0;
    
    for (const migration of migrations) {
      if (appliedVersions.has(migration.version)) {
        console.log(`[EnhancedMigrations] Already applied: ${migration.version}_${migration.name}`);
        continue;
      }
      
      // Special handling for Migration 9
      if (migration.version === 9) {
        console.log('[EnhancedMigrations] Using special handler for Migration 9');
        const success = this.processMigration9();
        if (success) {
          // Mark as completed in migrations table
          this.db.prepare(`
            INSERT INTO migrations (id, filename, status)
            VALUES (?, ?, 'completed')
          `).run(9, migration.filename);
          successCount++;
        } else {
          failCount++;
          console.error('[EnhancedMigrations] Migration 9 failed, stopping migration process');
          break;
        }
        continue;
      }
      
      // Use SafeMigrationRunner for other migrations
      const result = await this.safeMigrationRunner.runMigration(
        migration.version,
        migration.filename,
        migration.sql
      );
      
      if (result.success) {
        successCount++;
      } else {
        failCount++;
        console.error(`[EnhancedMigrations] Migration ${migration.version} failed, stopping process`);
        break;
      }
    }
    
    console.log(`[EnhancedMigrations] Complete: ${successCount} successful, ${failCount} failed`);
    
    if (failCount > 0) {
      throw new Error(`Migration process failed with ${failCount} errors`);
    }
  }
  
  /**
   * Get migration status with enhanced details
   */
  getMigrationStatus(): any {
    return this.safeMigrationRunner.getMigrationStatus();
  }
  
  /**
   * Check migration health
   */
  checkMigrationHealth(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check for failed migrations
    const status = this.getMigrationStatus();
    const failed = status.filter((m: any) => m.status === 'failed');
    if (failed.length > 0) {
      issues.push(`${failed.length} failed migrations detected`);
    }
    
    // Check for missing critical columns
    const criticalChecks = [
      { table: 'products', column: 'organization_id' },
      { table: 'colors', column: 'organization_id' },
      { table: 'products', column: 'external_id' },
      { table: 'colors', column: 'external_id' }
    ];
    
    for (const check of criticalChecks) {
      if (!this.helpers.columnExists(check.table, check.column)) {
        issues.push(`Missing critical column: ${check.table}.${check.column}`);
      }
    }
    
    // Check for missing critical tables
    const criticalTables = ['organizations', 'organization_members', 'users'];
    for (const table of criticalTables) {
      if (!this.helpers.tableExists(table)) {
        issues.push(`Missing critical table: ${table}`);
      }
    }
    
    return {
      healthy: issues.length === 0,
      issues
    };
  }
}