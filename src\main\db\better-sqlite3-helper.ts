/**
 * Helper module to load better-sqlite3 correctly in both development and production
 */

// Function to get the correct better-sqlite3 module
export function getBetterSqlite3() {
  try {
    console.log('[DB] Loading better-sqlite3 with direct require...');

    // Use dynamic import for ES modules compatibility
    const module = require('better-sqlite3');
    return module;
  } catch (error) {
    console.error('[DB] Failed to load better-sqlite3:', error);
    // Return a mock object to prevent crashes during migration
    console.warn('[DB] Returning default export wrapper for better-sqlite3');
    try {
      // Try importing as ES module
      const Database = require('better-sqlite3').default || require('better-sqlite3');
      return Database;
    } catch (fallbackError) {
      console.error('[DB] All attempts to load better-sqlite3 failed:', fallbackError);
      throw error;
    }
  }
}

// Create a database instance with proper error handling
export function createDatabase(dbPath: string, options?: any) {
  try {
    console.log(`[DB] Creating database at: ${dbPath}`);

    // Get database constructor
    const Database = getBetterSqlite3();

    // Create instance
    return new Database(dbPath, options);
  } catch (error) {
    console.error(`[DB] Error creating database at ${dbPath}:`, error);
    throw error;
  }
}
