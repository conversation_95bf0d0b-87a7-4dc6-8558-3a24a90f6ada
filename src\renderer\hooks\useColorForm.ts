/**
 * @file useColorForm.ts
 * @description Custom hook for managing color form state with validation
 */

import { useReducer, useCallback, useEffect } from 'react';
import { ColorEntry, NewColorEntry } from '../../shared/types/color.types';
import { isValidHex, isValidCMYK, hexToRgb, parseCMYK } from '../../shared/utils/color';

// Types
export interface ColorFormState {
  formData: NewColorEntry;
  validation: {
    product: boolean;
    name: boolean;
    hex: boolean;
    code: boolean;
    cmyk: boolean;
    isFormValid: boolean;
  };
  isSubmitting: boolean;
  error: string | null;
  rgb: { r: number; g: number; b: number } | null;
  cmykValues: { c: number; m: number; y: number; k: number } | null;
  isDarkColor: boolean;
}

// Define action types
type ColorFormAction =
  | { type: 'SET_FIELD'; field: keyof NewColorEntry; value: string }
  | { type: 'SET_HEX_FROM_PICKER'; value: string }
  | { type: 'SET_FORM_DATA'; formData: NewColorEntry }
  | { type: 'SUBMISSION_STARTED' }
  | { type: 'SUBMISSION_SUCCESSFUL' }
  | { type: 'SUBMISSION_FAILED'; error: string }
  | { type: 'RESET_FORM' }
  | { type: 'VALIDATE_FORM' };

// Default form values factory
export const getDefaultColorData = (defaultProduct: string = 'My Collection'): NewColorEntry => ({
  product: defaultProduct,
  name: '',
  code: '',
  hex: '#000000',
  cmyk: '',
  notes: '',
});

// Helper function to determine if a color is dark
const calculateIsDarkColor = (rgb: { r: number; g: number; b: number } | null): boolean => {
  if (!rgb) {return false;}

  // Calculate relative luminance using the formula:
  // L = 0.299 * R + 0.587 * G + 0.114 * B
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;

  // Return true if the luminance is less than 0.5 (dark color)
  return luminance < 0.5;
};

// Helper function to validate the entire form
const validateForm = (data: NewColorEntry): ColorFormState['validation'] => {
  const productValid = data.product.trim() !== '';
  const nameValid = data.name.trim() !== '';
  const hexValid = isValidHex(data.hex);
  const codeValid = data.code.trim() !== '';
  const cmykValid = isValidCMYK(data.cmyk);

  // Form is valid if all required fields are filled
  const isFormValid = productValid && nameValid && hexValid && codeValid && cmykValid;

  return {
    product: productValid,
    name: nameValid,
    hex: hexValid,
    code: codeValid,
    cmyk: cmykValid,
    isFormValid
  };
};

/**
 * Reducer function for color form state
 */
function colorFormReducer(state: ColorFormState, action: ColorFormAction): ColorFormState {
  switch (action.type) {
    case 'SET_FIELD': {
      const updatedFormData = { ...state.formData, [action.field]: action.value };

      // Update validation for specific field
      const fieldValidation = { ...state.validation };

      if (action.field === 'hex') {
        fieldValidation.hex = isValidHex(action.value);
      } else if (action.field === 'cmyk') {
        fieldValidation.cmyk = isValidCMYK(action.value);
      } else if (action.field === 'name') {
        fieldValidation.name = action.value.trim() !== '';
      } else if (action.field === 'code') {
        fieldValidation.code = action.value.trim() !== '';
      } else if (action.field === 'product') {
        fieldValidation.product = action.value.trim() !== '';
      }

      // Update RGB and CMYK values if hex changes
      const rgb = action.field === 'hex' ? hexToRgb(action.value) : state.rgb;
      const cmykValues = action.field === 'cmyk' ? parseCMYK(action.value) : state.cmykValues;

      // Check if form is valid
      fieldValidation.isFormValid =
        fieldValidation.product &&
        fieldValidation.name &&
        fieldValidation.hex &&
        fieldValidation.code &&
        fieldValidation.cmyk;

      return {
        ...state,
        formData: updatedFormData,
        validation: fieldValidation,
        rgb,
        cmykValues,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SET_HEX_FROM_PICKER': {
      const updatedFormData = { ...state.formData, hex: action.value };
      const rgb = hexToRgb(action.value);

      // Generate validation based on current form values
      const validation = validateForm(updatedFormData);

      return {
        ...state,
        formData: updatedFormData,
        validation,
        rgb,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SET_FORM_DATA': {
      const validation = validateForm(action.formData);
      const rgb = hexToRgb(action.formData.hex);
      const cmykValues = parseCMYK(action.formData.cmyk);

      return {
        ...state,
        formData: action.formData,
        validation,
        rgb,
        cmykValues,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SUBMISSION_STARTED':
      return {
        ...state,
        isSubmitting: true,
        error: null
      };

    case 'SUBMISSION_SUCCESSFUL':
      return {
        ...state,
        isSubmitting: false
      };

    case 'SUBMISSION_FAILED':
      return {
        ...state,
        isSubmitting: false,
        error: action.error
      };

    case 'RESET_FORM':
      return {
        ...state,
        formData: getDefaultColorData(),
        validation: validateForm(getDefaultColorData()),
        isSubmitting: false,
        error: null,
        rgb: null,
        cmykValues: null,
        isDarkColor: false
      };

    case 'VALIDATE_FORM':
      return {
        ...state,
        validation: validateForm(state.formData)
      };

    default:
      return state;
  }
}

/**
 * Custom hook for managing color form state
 */
export function useColorForm(initialColor?: ColorEntry, defaultProduct?: string) {
  // Initialize state
  const initialFormData = initialColor
    ? {
        product: initialColor.product,
        name: initialColor.name,
        code: initialColor.code,
        hex: initialColor.hex,
        cmyk: initialColor.cmyk,
        notes: initialColor.notes || '',
      }
    : getDefaultColorData(defaultProduct);

  const initialState: ColorFormState = {
    formData: initialFormData,
    validation: validateForm(initialFormData),
    isSubmitting: false,
    error: null,
    rgb: hexToRgb(initialFormData.hex),
    cmykValues: parseCMYK(initialFormData.cmyk),
    isDarkColor: calculateIsDarkColor(hexToRgb(initialFormData.hex))
  };

  const [state, dispatch] = useReducer(colorFormReducer, initialState);

  // Update form when initialColor changes
  useEffect(() => {
    if (initialColor) {
      dispatch({
        type: 'SET_FORM_DATA',
        formData: {
          product: initialColor.product,
          name: initialColor.name,
          code: initialColor.code,
          hex: initialColor.hex,
          cmyk: initialColor.cmyk,
          notes: initialColor.notes || '',
        }
      });
    } else {
      dispatch({ type: 'RESET_FORM' });
    }
  }, [initialColor]);

  // Action handlers
  const setField = useCallback((field: keyof NewColorEntry, value: string) => {
    dispatch({ type: 'SET_FIELD', field, value });
  }, []);

  const setHexFromColorPicker = useCallback((hex: string) => {
    dispatch({ type: 'SET_HEX_FROM_PICKER', value: hex });
  }, []);

  const startSubmission = useCallback(() => {
    dispatch({ type: 'VALIDATE_FORM' });

    if (state.validation.isFormValid) {
      dispatch({ type: 'SUBMISSION_STARTED' });
      return true;
    }

    return false;
  }, [state.validation.isFormValid]);

  const submissionSuccessful = useCallback(() => {
    dispatch({ type: 'SUBMISSION_SUCCESSFUL' });
  }, []);

  const submissionFailed = useCallback((error: string) => {
    dispatch({ type: 'SUBMISSION_FAILED', error });
  }, []);

  const resetForm = useCallback(() => {
    dispatch({ type: 'RESET_FORM' });
  }, []);

  return {
    ...state,
    setField,
    setHexFromColorPicker,
    startSubmission,
    submissionSuccessful,
    submissionFailed,
    resetForm,
    rgbString: state.rgb ? `rgb(${state.rgb.r}, ${state.rgb.g}, ${state.rgb.b})` : '',
  };
}