/**
 * @file organization.ipc.ts
 * @description IPC handlers for organization operations
 */

import { ipcMain } from 'electron';
import { OrganizationService } from '../db/services/organization.service';
import type { 
  CreateOrganizationRequest
} from '../../shared/types/organization.types';
import Database from 'better-sqlite3';
import { oauthService } from '../services/oauth.service';
import Store from 'electron-store';

// IPC Channel names
export enum OrganizationChannels {
  CREATE = 'organization:create',
  GET_ALL = 'organization:getAll',
  GET_BY_ID = 'organization:getById',
  GET_CURRENT = 'organization:getCurrent',
  SET_CURRENT = 'organization:setCurrent',
  GET_MEMBERS = 'organization:getMembers',
  ADD_MEMBER = 'organization:addMember',
  UPDATE_MEMBER_ROLE = 'organization:updateMemberRole',
  REMOVE_MEMBER = 'organization:removeMember',
  UPDATE_SETTINGS = 'organization:updateSettings',
  DELETE = 'organization:delete',
  INVITE_MEMBER = 'organization:inviteMember',
  ACCEPT_INVITATION = 'organization:acceptInvitation',
  GET_PENDING_INVITATIONS = 'organization:getPendingInvitations',
  REVOKE_INVITATION = 'organization:revokeInvitation'
}

// Store the current organization ID in memory (per session)
let currentOrganizationId: string | null = null;

// Store instance for persistence
const store = new Store();

/**
 * Helper function to safely extract error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {return error.message;}
  if (typeof error === 'string') {return error;}
  return String(error);
}

/**
 * Initialize the current organization ID from persisted storage
 */
function initializeCurrentOrganizationId(): void {
  const persistedOrgId = store.get('currentOrganizationId') as string | null;
  if (persistedOrgId) {
    currentOrganizationId = persistedOrgId;
    // Also set in global for other handlers
    (global as any).currentOrganizationId = persistedOrgId;
    console.log('[OrganizationIPC] Restored current organization ID from storage:', persistedOrgId);
  }
}

/**
 * Register IPC handlers for organization operations
 */
export function registerOrganizationHandlers(organizationService: OrganizationService, db: Database.Database): void {
  // Initialize current organization ID from persisted storage
  initializeCurrentOrganizationId();
  
  // Create organization
  ipcMain.handle(OrganizationChannels.CREATE, async (event, data: CreateOrganizationRequest) => {
    try {
      console.log('[OrganizationIPC] Creating organization:', data.name);
      
      // Get current user ID from auth service
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        console.error('[OrganizationIPC] No authenticated user found');
        return { success: false, error: 'User not authenticated' };
      }
      
      const organization = await organizationService.createOrganization(data.name, user.id);
      
      // Set as current organization
      currentOrganizationId = organization.external_id;
      
      // Persist the current organization ID
      store.set('currentOrganizationId', organization.external_id);
      
      // Also set in global for other handlers
      (global as any).currentOrganizationId = organization.external_id;
      
      return { success: true, data: organization };
    } catch (error) {
      console.error('[OrganizationIPC] Error creating organization:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Get all organizations for user
  ipcMain.handle(OrganizationChannels.GET_ALL, async (event) => {
    try {
      // Get current user from auth service
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        console.error('[OrganizationIPC] No authenticated user found');
        return { success: false, error: 'User not authenticated' };
      }
      
      console.log('[OrganizationIPC] Getting organizations for user:', user.id);
      
      // Sync organizations from Supabase first
      await organizationService.syncOrganizationsFromSupabase(user.id);
      
      // Then get all organizations
      const organizations = await organizationService.getOrganizationsForUser(user.id);
      return { success: true, data: organizations };
    } catch (error) {
      console.error('[OrganizationIPC] Error getting organizations:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Get organization by ID
  ipcMain.handle(OrganizationChannels.GET_BY_ID, async (event, organizationId: string) => {
    try {
      const organization = await organizationService.getOrganization(organizationId);
      if (!organization) {
        return { success: false, error: 'Organization not found' };
      }
      return { success: true, data: organization };
    } catch (error) {
      console.error('[OrganizationIPC] Error getting organization:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Get current organization
  ipcMain.handle(OrganizationChannels.GET_CURRENT, async () => {
    try {
      if (!currentOrganizationId) {
        return { success: false, error: 'No organization selected' };
      }
      
      const organization = await organizationService.getOrganization(currentOrganizationId);
      if (!organization) {
        currentOrganizationId = null;
        return { success: false, error: 'Current organization not found' };
      }
      
      return { success: true, data: organization };
    } catch (error) {
      console.error('[OrganizationIPC] Error getting current organization:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Set current organization
  ipcMain.handle(OrganizationChannels.SET_CURRENT, async (event, organizationId: string) => {
    try {
      const organization = await organizationService.getOrganization(organizationId);
      if (!organization) {
        return { success: false, error: 'Organization not found' };
      }
      
      currentOrganizationId = organizationId;
      
      // Persist the current organization ID
      store.set('currentOrganizationId', organizationId);
      
      // Also set in global for other handlers
      (global as any).currentOrganizationId = organizationId;
      
      console.log('[OrganizationIPC] Current organization set to:', organizationId);
      
      return { success: true, data: organization };
    } catch (error) {
      console.error('[OrganizationIPC] Error setting current organization:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Get organization members
  ipcMain.handle(OrganizationChannels.GET_MEMBERS, async (event, organizationId: string) => {
    try {
      // First sync members from Supabase
      await organizationService.syncMembersFromSupabase(organizationId);
      
      // Then get all members
      const members = await organizationService.getMembers(organizationId);
      return { success: true, data: members };
    } catch (error) {
      console.error('[OrganizationIPC] Error getting members:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Add member
  ipcMain.handle(OrganizationChannels.ADD_MEMBER, async (event, organizationId: string, userId: string, role: string, invitedBy: string) => {
    try {
      await organizationService.addMember(organizationId, userId, role as any, invitedBy);
      return { success: true };
    } catch (error) {
      console.error('[OrganizationIPC] Error adding member:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Update member role
  ipcMain.handle(OrganizationChannels.UPDATE_MEMBER_ROLE, async (event, data: { organizationId: string; userId: string; role: string }) => {
    try {
      await organizationService.updateMemberRole(data.organizationId, data.userId, data.role as any);
      return { success: true };
    } catch (error) {
      console.error('[OrganizationIPC] Error updating member role:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Remove member
  ipcMain.handle(OrganizationChannels.REMOVE_MEMBER, async (event, data: { organizationId: string; userId: string }) => {
    try {
      await organizationService.removeMember(data.organizationId, data.userId);
      return { success: true };
    } catch (error) {
      console.error('[OrganizationIPC] Error removing member:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Update organization settings
  ipcMain.handle(OrganizationChannels.UPDATE_SETTINGS, async (event, organizationId: string, settings: any) => {
    try {
      await organizationService.updateOrganization(organizationId, { settings });
      return { success: true };
    } catch (error) {
      console.error('[OrganizationIPC] Error updating settings:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Delete organization
  ipcMain.handle(OrganizationChannels.DELETE, async (event, organizationId: string) => {
    try {
      // Get current user from auth service
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }
      
      // Delete the organization
      await organizationService.deleteOrganization(organizationId, user.id);
      
      // Clear current organization if it was deleted
      if (currentOrganizationId === organizationId) {
        currentOrganizationId = null;
        store.delete('currentOrganizationId');
        delete (global as any).currentOrganizationId;
      }
      
      return { success: true };
    } catch (error) {
      console.error('[OrganizationIPC] Error deleting organization:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Invite member
  ipcMain.handle(OrganizationChannels.INVITE_MEMBER, async (event, data: { organizationId: string; email: string; role: string }) => {
    try {
      // Get current user from auth service
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }
      
      console.log('[OrganizationIPC] Inviting member:', data);
      
      const result = await organizationService.inviteMember(
        data.organizationId,
        data.email,
        data.role as 'admin' | 'member',
        user.id
      );
      
      return result;
    } catch (error) {
      console.error('[OrganizationIPC] Error inviting member:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Accept invitation
  ipcMain.handle(OrganizationChannels.ACCEPT_INVITATION, async (event, token: string) => {
    try {
      // Get current user from auth service
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }
      
      const result = await organizationService.acceptInvitation(token, user.id);
      
      return result;
    } catch (error) {
      console.error('[OrganizationIPC] Error accepting invitation:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Get pending invitations
  ipcMain.handle(OrganizationChannels.GET_PENDING_INVITATIONS, async (event, organizationId: string) => {
    try {
      const invitations = await organizationService.getPendingInvitations(organizationId);
      return { success: true, data: invitations };
    } catch (error) {
      console.error('[OrganizationIPC] Error getting invitations:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
  
  // Revoke invitation
  ipcMain.handle(OrganizationChannels.REVOKE_INVITATION, async (event, data: { organizationId: string; invitationId: string }) => {
    try {
      const success = await organizationService.revokeInvitation(data.organizationId, data.invitationId);
      return { success };
    } catch (error) {
      console.error('[OrganizationIPC] Error revoking invitation:', error);
      return { success: false, error: getErrorMessage(error) };
    }
  });
}

// Export helper to get current organization ID for other handlers
export function getCurrentOrganizationId(): string | null {
  return currentOrganizationId;
}

// Export helper to set current organization ID (for auth flow)
export function setCurrentOrganizationId(organizationId: string | null): void {
  currentOrganizationId = organizationId;
  
  if (organizationId) {
    store.set('currentOrganizationId', organizationId);
    (global as any).currentOrganizationId = organizationId;
  } else {
    store.delete('currentOrganizationId');
    delete (global as any).currentOrganizationId;
  }
}
